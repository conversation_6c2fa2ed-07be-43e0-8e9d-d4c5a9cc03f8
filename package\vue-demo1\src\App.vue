<script setup lang="ts">
  import { aisString, isNumber, isFunction, isArray, isObject } from "@libc/ui"

  // 测试类型推断
  const testValue: unknown = "hello"

  if (aisString(testValue)) {
    // 这里 testValue 应该被推断为 string 类型
    console.log("String length:", testValue.length)
  }

  if (isNumber(testValue)) {
    // 这里 testValue 应该被推断为 number 类型
    console.log("Number value:", testValue.toFixed(2))
  }

  console.log("Type checks:", {
    aisString: aisString("test"),
    isNumber: isNumber(123),
    isFunction: isFunction(() => { }),
    isArray: isArray([1, 2, 3]),
    isObject: isObject({ a: 1 })
  })
</script>

<template>
  <div>{{aisString('2222')}}</div>
  <h1>You did it!</h1>
  <p>
    Visit <a href="https://vuejs.org/" target="_blank" rel="noopener">vuejs.org</a> to read the
    documentation
  </p>
</template>

<style scoped></style>