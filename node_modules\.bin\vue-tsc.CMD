@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules\vue-tsc\bin\node_modules;D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules\vue-tsc\node_modules;D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules;D:\前端code\微前端\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules\vue-tsc\bin\node_modules;D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules\vue-tsc\node_modules;D:\前端code\微前端\node_modules\.pnpm\vue-tsc@3.0.4_typescript@5.8.3\node_modules;D:\前端code\微前端\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\vue-tsc\bin\vue-tsc.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\vue-tsc\bin\vue-tsc.js" %*
)
