{"name": "微前端", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev:demo1": "cd package/vue-demo1 & pnpm dev", "dev:demo2": "cd package/vue-demo2 & pnpm dev", "build:demo1": "cd package/vue-demo1 & pnpm build", "build:demo2": "cd package/vue-demo2 & pnpm build"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.13.1", "dependencies": {"@libc/ui": "workspace:*", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^22.16.5", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "typescript": "~5.8.0", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.6.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.4"}}