import{W as Z,c as C,o as u,X as y,d as V,Y as R,s as L,Z as I,g as a,$ as m,a0 as k,a1 as P,b as e,a as w,w as s,u as o,a2 as D,t as v,e as S,y as l,a3 as N,A as n,a4 as M,F as g,M as r,a5 as j}from"./index-DOp68rd-.js";const B={},U={viewBox:"0 0 566 154",fill:"none",xmlns:"http://www.w3.org/2000/svg"};function O(E,d){return u(),C("svg",U,d[0]||(d[0]=[y('<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 198" width="128" height="128"><path fill="#41B883" d="M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"></path><path fill="#41B883" d="m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"></path><path fill="#35495E" d="M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"></path></svg><path d="M152.571 118V46.1333H176.8C183.576 46.1333 189.77 47.468 195.383 50.1373C200.995 52.8067 205.478 56.8449 208.832 62.252C212.186 67.5907 213.863 74.1956 213.863 82.0667C213.863 89.8693 212.186 96.4742 208.832 101.881C205.478 107.288 200.995 111.327 195.383 113.996C189.77 116.665 183.576 118 176.8 118H152.571ZM175.157 104.653C181.796 104.653 187.306 102.874 191.687 99.3147C196.067 95.6871 198.257 89.9378 198.257 82.0667C198.257 74.1956 196.067 68.4462 191.687 64.8187C187.306 61.1911 181.796 59.3773 175.157 59.3773H167.971V104.653H175.157ZM270.18 100.444C269.153 105.988 266.381 110.437 261.864 113.791C257.347 117.144 251.905 118.821 245.54 118.821C239.928 118.821 235.102 117.555 231.064 115.023C227.094 112.422 224.083 109.068 222.029 104.961C219.976 100.855 218.949 96.5427 218.949 92.0253C218.949 87.4396 219.873 83.1276 221.721 79.0893C223.638 75.0511 226.513 71.7658 230.345 69.2333C234.247 66.7009 238.969 65.4347 244.513 65.4347C250.263 65.4347 255.054 66.6667 258.887 69.1307C262.72 71.5947 265.526 74.7431 267.305 78.576C269.153 82.4089 270.077 86.4471 270.077 90.6907C270.077 92.2649 270.009 93.8049 269.872 95.3107H232.707C233.254 99.0067 234.623 101.916 236.813 104.037C239.072 106.091 241.981 107.117 245.54 107.117C248.415 107.117 250.776 106.57 252.624 105.475C254.472 104.311 255.67 102.634 256.217 100.444H270.18ZM244.513 75.9067C241.228 75.9067 238.627 76.7622 236.711 78.4733C234.794 80.116 233.528 82.7169 232.912 86.276H255.807C255.601 83.2644 254.506 80.8004 252.521 78.884C250.537 76.8991 247.867 75.9067 244.513 75.9067ZM302.77 118H289.629L270.738 66.256H285.317L296.2 99.7253L306.98 66.256H321.661L302.77 118ZM359.731 118H344.331V59.3773H323.079V46.1333H380.983V59.3773H359.731V118ZM402.364 118.821C397.163 118.821 392.474 117.692 388.299 115.433C384.124 113.106 380.873 109.924 378.546 105.885C376.219 101.779 375.055 97.1929 375.055 92.128C375.055 87.0631 376.219 82.5116 378.546 78.4733C380.873 74.3667 384.124 71.184 388.299 68.9253C392.474 66.5982 397.163 65.4347 402.364 65.4347C407.566 65.4347 412.255 66.5982 416.43 68.9253C420.605 71.184 423.856 74.3667 426.183 78.4733C428.51 82.5116 429.674 87.0631 429.674 92.128C429.674 97.1929 428.51 101.779 426.183 105.885C423.856 109.924 420.605 113.106 416.43 115.433C412.255 117.692 407.566 118.821 402.364 118.821ZM402.364 106.501C406.197 106.501 409.311 105.167 411.707 102.497C414.103 99.828 415.3 96.3716 415.3 92.128C415.3 87.816 414.103 84.3253 411.707 81.656C409.311 78.9867 406.197 77.652 402.364 77.652C398.531 77.652 395.417 78.9867 393.022 81.656C390.626 84.3253 389.428 87.816 389.428 92.128C389.428 96.3716 390.626 99.828 393.022 102.497C395.417 105.167 398.531 106.501 402.364 106.501ZM462.12 118.821C456.918 118.821 452.229 117.692 448.054 115.433C443.879 113.106 440.628 109.924 438.301 105.885C435.974 101.779 434.81 97.1929 434.81 92.128C434.81 87.0631 435.974 82.5116 438.301 78.4733C440.628 74.3667 443.879 71.184 448.054 68.9253C452.229 66.5982 456.918 65.4347 462.12 65.4347C467.321 65.4347 472.01 66.5982 476.185 68.9253C480.36 71.184 483.611 74.3667 485.938 78.4733C488.265 82.5116 489.429 87.0631 489.429 92.128C489.429 97.1929 488.265 101.779 485.938 105.885C483.611 109.924 480.36 113.106 476.185 115.433C472.01 117.692 467.321 118.821 462.12 118.821ZM462.12 106.501C465.952 106.501 469.067 105.167 471.462 102.497C473.858 99.828 475.056 96.3716 475.056 92.128C475.056 87.816 473.858 84.3253 471.462 81.656C469.067 78.9867 465.952 77.652 462.12 77.652C458.287 77.652 455.172 78.9867 452.777 81.656C450.381 84.3253 449.184 87.816 449.184 92.128C449.184 96.3716 450.381 99.828 452.777 102.497C455.172 105.167 458.287 106.501 462.12 106.501ZM512.327 118H498.056V43.772H512.327V118ZM542.494 118.821C536.129 118.821 530.961 117.179 526.991 113.893C523.09 110.54 521.002 106.091 520.729 100.547H533.049C533.322 102.874 534.281 104.722 535.923 106.091C537.634 107.391 539.825 108.041 542.494 108.041C544.684 108.041 546.464 107.562 547.833 106.604C549.27 105.646 549.989 104.448 549.989 103.011C549.989 101.094 549.167 99.7596 547.525 99.0067C545.882 98.2538 543.281 97.5693 539.722 96.9533C536.026 96.2689 533.014 95.516 530.687 94.6947C528.36 93.8733 526.341 92.4018 524.63 90.28C522.987 88.0898 522.166 85.0098 522.166 81.04C522.166 78.0284 522.953 75.3591 524.527 73.032C526.17 70.6364 528.394 68.7884 531.201 67.488C534.007 66.1191 537.155 65.4347 540.646 65.4347C546.874 65.4347 551.905 66.9747 555.738 70.0547C559.639 73.1347 561.727 77.2071 562.001 82.272H549.578C549.304 80.1502 548.312 78.5076 546.601 77.344C544.958 76.112 543.11 75.496 541.057 75.496C539.003 75.496 537.361 75.9409 536.129 76.8307C534.897 77.7204 534.281 78.9524 534.281 80.5267C534.281 82.4431 535.068 83.7436 536.642 84.428C538.285 85.044 540.851 85.5916 544.342 86.0707C548.106 86.6182 551.186 87.3027 553.582 88.124C556.046 88.8769 558.168 90.3827 559.947 92.6413C561.727 94.9 562.617 98.1853 562.617 102.497C562.617 107.425 560.769 111.395 557.073 114.407C553.445 117.35 548.585 118.821 542.494 118.821Z" fill="currentColor"></path>',2)]))}const A=Z(B,[["render",O]]),F="8.0.0",$={"h-full":"","w-full":"",flex:"","of-auto":""},h={flex:"~ col gap2",ma:"","h-full":"","max-w-300":"","w-full":"",px20:""},K={flex:"~ col","mt-20":"","items-center":""},z={flex:"~","mt--10":"","items-center":"","justify-center":""},G={key:0,mb6:"","mt--1":"","text-center":"","text-sm":"",flex:"~ gap-1"},W={op40:""},X={flex:"~ gap2 wrap"},Y={"theme-card-green":"",p4:"",flex:"~ col auto"},q={flex:"~ gap-6 wrap","mt-5":"","items-center":"","justify-center":""},J={flex:"~ gap-1","cursor-default":"","items-center":"","justify-center":"","pb-2":"","text-sm":"",op40:""},Q={key:0,flex:"~ gap-1","cursor-default":"","items-center":"","justify-center":"","pb-8":"","text-sm":"",op40:""},t1=V({__name:"overview",setup(E){const{vueVersion:d}=R(),x=L(1),p=L(0);function _(i){let t=0;for(const f of i)t++,f.children?.length&&(t+=_(f.children));return t}function H(i){x.value=i?.routes?.length||1}function T(i){const t=k(i);t.inspectorId==="components"&&(p.value=_(t.rootNodes))}return I(()=>{a.value.getRouterInfo().then(i=>{x.value=i?.routes?.length||1}),a.functions.on(m.ROUTER_INFO_UPDATED,H),a.value.getInspectorTree({inspectorId:"components",filter:""}).then(i=>{const t=k(i);p.value=_(t)})}),a.functions.on(m.INSPECTOR_TREE_UPDATED,T),P(()=>{a.functions.off(m.INSPECTOR_TREE_UPDATED,T),a.functions.off(m.ROUTER_INFO_UPDATED,H)}),(i,t)=>{const f=A,b=N("RouterLink");return u(),C("div",$,[e("div",h,[t[29]||(t[29]=e("div",{"flex-auto":""},null,-1)),e("div",K,[e("div",z,[s(f,{"h-18":""})]),o(D)?w("",!0):(u(),C("div",G,[t[0]||(t[0]=e("span",{op40:""}," Vue DevTools ",-1)),e("code",W,"v"+v(o(F)),1)]))]),e("div",X,[e("div",Y,[t[1]||(t[1]=e("div",{"i-logos-vue":"","text-3xl":""},null,-1)),e("code",null,"v"+v(o(d)),1)]),s(b,{flex:"~ col auto",to:"/pages",replace:"","min-w-40":"","theme-card-lime":"",p4:""},{default:l(()=>[t[2]||(t[2]=e("div",{"i-carbon-tree-view-alt":"","text-3xl":""},null,-1)),e("div",null,v(o(x))+" pages",1)]),_:1,__:[2]}),o(p)?(u(),S(b,{key:0,flex:"~ col auto",to:"/components",replace:"","min-w-40":"","theme-card-lime":"",p4:""},{default:l(()=>[t[3]||(t[3]=e("div",{"i-carbon-assembly-cluster":"","text-3xl":""},null,-1)),e("div",null,v(o(p))+" components",1)]),_:1,__:[3]})):w("",!0)]),e("div",q,[t[5]||(t[5]=e("a",{href:"https://github.com/vuejs/devtools",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-blue",transition:""},[e("div",{"i-carbon-star":""}),n(" Star on GitHub ")],-1)),t[6]||(t[6]=e("a",{href:"https://github.com/vuejs/devtools/discussions/111",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-yellow",transition:""},[e("div",{"i-carbon-data-enrichment":""}),n(" Ideas & Suggestions ")],-1)),t[7]||(t[7]=e("a",{href:"https://github.com/vuejs/devtools/discussions/112",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-lime",transition:""},[e("div",{"i-carbon-plan":""}),n(" Project Roadmap ")],-1)),t[8]||(t[8]=e("a",{href:"https://github.com/vuejs/devtools/issues",target:"_blank",flex:"~ gap1","items-center":"",op50:"",hover:"op100 text-rose",transition:""},[e("div",{"i-carbon-debug":""}),n(" Bug Reports ")],-1)),s(b,{to:"/settings",flex:"~ gap1",replace:"","inline-block":"","items-center":"",op50:"","hover:op80":""},{default:l(()=>t[4]||(t[4]=[e("div",{"i-carbon-settings":""},null,-1),n(" Settings ",-1)])),_:1,__:[4]})]),t[30]||(t[30]=e("div",{"flex-auto":""},null,-1)),e("div",J,[t[15]||(t[15]=n(" Press ",-1)),o(M)()?(u(),C(g,{key:0},[s(o(r),null,{default:l(()=>t[9]||(t[9]=[n(" ⌘ Command ",-1)])),_:1,__:[9]}),t[11]||(t[11]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[10]||(t[10]=[n(" K ",-1)])),_:1,__:[10]})],64)):(u(),C(g,{key:1},[s(o(r),null,{default:l(()=>t[12]||(t[12]=[n(" Alt ",-1)])),_:1,__:[12]}),t[14]||(t[14]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[13]||(t[13]=[n(" K ",-1)])),_:1,__:[13]})],64)),t[16]||(t[16]=n(" to toggle Command Palette ",-1))]),!o(j)&&!o(D)?(u(),C("div",Q,[t[27]||(t[27]=n(" Press ",-1)),o(M)()?(u(),C(g,{key:0},[s(o(r),null,{default:l(()=>t[17]||(t[17]=[n(" ⇧ Shift ",-1)])),_:1,__:[17]}),t[20]||(t[20]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[18]||(t[18]=[n(" ⌥ Option ",-1)])),_:1,__:[18]}),t[21]||(t[21]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[19]||(t[19]=[n(" D ",-1)])),_:1,__:[19]})],64)):(u(),C(g,{key:1},[s(o(r),null,{default:l(()=>t[22]||(t[22]=[n(" Shift ",-1)])),_:1,__:[22]}),t[25]||(t[25]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[23]||(t[23]=[n(" Alt ",-1)])),_:1,__:[23]}),t[26]||(t[26]=e("span",null,"+",-1)),s(o(r),null,{default:l(()=>t[24]||(t[24]=[n(" D ",-1)])),_:1,__:[24]})],64)),t[28]||(t[28]=n(" to toggle DevTools ",-1))])):w("",!0)])])}}});export{t1 as default};
