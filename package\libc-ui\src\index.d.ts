/**
 * 公共方法类型声明
 */

// 判断是否是字符串
export declare const aisString: (value: any) => value is string;

// 判断是否是数字
export declare const isNumber: (value: any) => value is number;

// 判断是否是函数
export declare const isFunction: (value: any) => value is Function;

// 判断是否是数组
export declare const isArray: (value: any) => value is any[];

// 判断是否是对象
export declare const isObject: (value: any) => value is object;
