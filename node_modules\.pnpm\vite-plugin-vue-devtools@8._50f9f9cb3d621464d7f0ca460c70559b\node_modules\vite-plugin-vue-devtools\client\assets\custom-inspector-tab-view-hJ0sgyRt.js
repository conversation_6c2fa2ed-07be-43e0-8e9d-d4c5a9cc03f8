import{d as m,s as d,Q as p,i as f,L as x,c as v,e as _,a as g,u as e,o as i,b as o,A as b,t as k,R as T,O as y}from"./index-DOp68rd-.js";/* empty css              */const B={key:0,flex:"~ col","h-full":"","items-center":"","justify-center":""},I={flex:"~ col gap2",mxa:"","items-center":""},R={"text-xl":""},w={"text-rose":""},V=m({__name:"custom-inspector-tab-view",setup(C){const a=x(),u=y(),n=d(!1),c=p(),r=f(()=>c.value.find(s=>s.name===a.params.name)?.pluginId);function l(){n.value=!0;const s=setTimeout(()=>{clearTimeout(s),u.replace("/overview")},2e3)}return(s,t)=>e(n)?(i(),v("div",B,[o("div",I,[t[1]||(t[1]=o("div",{"i-carbon-queued":"",mb2:"","text-5xl":"",op50:""},null,-1)),o("p",R,[o("code",w,k(e(a).params.name),1),t[0]||(t[0]=b(" not found ",-1))]),t[2]||(t[2]=o("p",{mt8:"","animate-pulse":""}," Redirecting to overview page... ",-1))])])):!e(n)&&e(r)?(i(),_(e(T),{key:1,id:e(a).params.name,"plugin-id":e(r),onLoadError:l},null,8,["id","plugin-id"])):g("",!0)}});export{V as default};
