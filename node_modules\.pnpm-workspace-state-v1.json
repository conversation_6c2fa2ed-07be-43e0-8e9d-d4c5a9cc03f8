{"lastValidatedTimestamp": 1754038707804, "projects": {"D:\\前端code\\微前端": {"name": "微前端", "version": "1.0.0"}, "D:\\前端code\\微前端\\package\\libc-ui": {"name": "@libc/ui", "version": "1.0.0"}, "D:\\前端code\\微前端\\package\\libc-utils": {"name": "@libc/utils", "version": "1.0.0"}, "D:\\前端code\\微前端\\package\\vue-demo1": {"name": "vue-demo1", "version": "0.0.0"}, "D:\\前端code\\微前端\\package\\vue-demo2": {"name": "vue-demo2", "version": "0.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": true, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": true, "production": true, "workspacePackagePatterns": ["package/*"]}, "filteredInstall": false}