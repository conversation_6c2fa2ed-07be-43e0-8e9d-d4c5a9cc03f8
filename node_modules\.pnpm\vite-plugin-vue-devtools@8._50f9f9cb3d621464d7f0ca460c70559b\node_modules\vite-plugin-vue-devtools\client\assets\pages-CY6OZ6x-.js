import{d as N,c as l,o as a,b as n,w as v,u as e,a6 as U,s as k,i as b,a7 as B,e as w,t as M,y,F as $,x as T,n as E,J as F,a as _,a8 as O,M as j,A as V,a9 as A,aa as z,Y as J,ab as I,f as K,Z as Y,g as D,$ as P,ac as L,a1 as W,ad as X,C as Z,ae as S,af as q}from"./index-DOp68rd-.js";import{_ as G}from"./SectionBlock-H9LBnZtw.js";import"./IconTitle.vue_vue_type_script_setup_true_lang-BXngeGY8.js";const H={"p-2":""},Q={class:"flex items-center justify-between"},ee=N({__name:"RouteMetaDetail",props:{meta:{}},emits:["close"],setup(R){return(r,i)=>(a(),l("div",H,[n("div",Q,[i[1]||(i[1]=n("span",{class:"font-500"},"Route meta detail",-1)),n("div",{class:"i-carbon-close cursor-pointer p1 $ui-text",onClick:i[0]||(i[0]=h=>r.$emit("close"))})]),v(e(U),{code:JSON.stringify(r.meta,null,2),lang:"json",lines:""},null,8,["code"])]))}}),te={block:"","cursor-pointer":""},ne={p2:""},oe=["onSubmit"],ae={flex:"~","items-center":"",p2:"","text-sm":"","font-mono":""},se={key:1},le=N({__name:"RoutePathItem",props:{route:{}},emits:["navigate"],setup(R,{emit:r}){const i=R,h=r;function m(t){return t.split(/(:\w+[?*+]?(?:\([^)]*\))?[?*+]?)/).filter(Boolean)}const d=k([]),c=b(()=>m(i.route.path));B(()=>{d.value=Array.from({length:c.value.length},()=>"")});const u=b(()=>c.value.map((t,o)=>t[0]===":"?d.value[o]:t).join("").replace(/\/+/g,"/")),s=b(()=>i.route.path.includes(":"));function p(){h("navigate",u.value)}return(t,o)=>e(s)?(a(),w(e(A),{key:1},{popper:y(({hide:f})=>[n("div",ne,[n("form",{flex:"~ col",onSubmit:F(()=>{p(),f()},["prevent"])},[e(s)?(a(),l($,{key:0},[o[0]||(o[0]=n("div",{px2:"","text-sm":"",op50:""}," Fill params and navigate: ",-1)),n("div",ae,[(a(!0),l($,null,T(e(c),(g,C)=>(a(),l($,{key:C},[g[0]===":"?(a(),w(e(O),{key:0,modelValue:e(d)[C],"onUpdate:modelValue":x=>e(d)[C]=x,"n-sm":"","w-20":"",placeholder:g.slice(1)},null,8,["modelValue","onUpdate:modelValue","placeholder"])):(a(),l("span",se,M(g),1))],64))),128))])],64)):_("",!0),v(e(j),{block:"",type:"primary"},{default:y(()=>o[1]||(o[1]=[V(" Navigate ",-1)])),_:1,__:[1]})],40,oe)])]),default:y(()=>[n("code",te,[(a(!0),l($,null,T(e(c),(f,g)=>(a(),l("span",{key:g,class:E(f[0]===":"?"text-gray border border-dashed rounded border-gray:50 px1":"")},M(f[0]===":"?f.slice(1):f),3))),128))])]),_:1})):(a(),l("button",{key:0,onClick:p},[n("code",null,M(t.route.path),1)]))}}),re={"w-full":""},ie={border:"b base","px-3":""},ue={key:0,"text-left":""},de={"w-20":"","pr-1":""},ce={flex:"","items-center":"","justify-end":""},pe={"text-sm":""},me={flex:"inline gap3","items-center":""},fe={op0:"","group-hover:op100":"",flex:"~ gap1"},ve=["onClick"],_e={"w-0":"","ws-nowrap":"","pr-1":"","text-left":"","text-sm":"","font-mono":"",op50:""},he={key:0,"w-50":"","ws-nowrap":"","pr-1":"","text-left":"","text-sm":"","font-mono":"",op50:"",hover:"text-primary op100"},ge=["title","onClick"],ye=N({__name:"RoutesTable",props:{pages:{},matched:{},matchedPending:{}},emits:["navigate","selectMeta"],setup(R){const r=R,i=b(()=>[...r.pages].sort((u,s)=>u.path.localeCompare(s.path))),h=b(()=>z.value),m=J();function d(u,s=0){const p=JSON.stringify(u,null,s);return p==="{}"?"-":p}const c=b(()=>i.value.some(u=>Object.keys(u.meta)?.length));return(u,s)=>{const p=le;return a(),l("div",null,[n("table",re,[n("thead",ie,[n("tr",null,[s[1]||(s[1]=n("th",{"text-left":""},null,-1)),s[2]||(s[2]=n("th",{"text-left":""}," Route Path ",-1)),s[3]||(s[3]=n("th",{"text-left":""}," Name ",-1)),e(c)?(a(),l("th",ue," Route Meta ")):_("",!0)])]),n("tbody",null,[(a(!0),l($,null,T(e(i),t=>(a(),l("tr",{key:t.name,class:"group","h-7":"",border:"b dashed transparent hover:base"},[n("td",de,[n("div",ce,[u.matched.find(o=>o.name===t.name)?(a(),w(e(I),{key:0,"bg-green-400:10":"","text-green-400":"",title:"active",textContent:"active"})):u.matchedPending.find(o=>o.name===t.name)?(a(),w(e(I),{key:1,"bg-teal-400:10":"","text-teal-400":"",title:"next",textContent:"next"})):_("",!0)])]),n("td",pe,[n("div",me,[v(p,{route:t,class:E(u.matched.find(o=>o.name===t.name)?"text-primary-400":u.matchedPending.find(o=>o.name===t.name)?"text-teal":""),onNavigate:s[0]||(s[0]=o=>u.$emit("navigate",o))},null,8,["route","class"]),n("div",fe,[t.meta?.file&&e(m).vitePluginDetected.value&&e(h)?(a(),l("button",{key:0,"text-sm":"",op40:"",hover:"op100 text-primary-400",title:"Open in editor",onClick:o=>e(K)(t.meta?.file)},s[4]||(s[4]=[n("div",{"i-carbon-script-reference":""},null,-1)]),8,ve)):_("",!0)])])]),n("td",_e,M(t.name),1),e(c)?(a(),l("td",he,[n("span",{"inline-block":"","w-50":"","cursor-pointer":"","overflow-hidden":"","text-ellipsis":"",title:d(t.meta,2),onClick:()=>u.$emit("selectMeta",t.meta)},M(d(t.meta)),9,ge)])):_("",!0)]))),128))])])])}}}),be={block:"","h-screen":"","of-auto":""},xe={"h-full":"",class:"grid grid-rows-[auto_1fr]"},ke={border:"b base",flex:"~ col gap1",px4:"",py3:""},$e={key:1,op50:""},we={key:0,"text-orange":"",op75:""},Re={key:1,op50:""},Ne=N({__name:"pages",setup(R){const r=k(""),i=k(null),h=k([]),m=b(()=>r.value===i.value?.path?[]:h.value),d=k([]),c=k();function u(t){d.value=t.routes,i.value=t.currentRoute,r.value=i.value?.path??"/"}function s(){m.value.length&&p(r.value)}function p(t){D.value.navigate(t)}return Y(()=>{D.value.getRouterInfo().then(t=>{u(t)}),D.functions.on(P.ROUTER_INFO_UPDATED,u)}),L(r,()=>{r.value!==i.value?.path&&D.value.getMatchedRoutes(r.value).then(t=>{h.value=t})}),W(()=>{D.functions.off(P.ROUTER_INFO_UPDATED,u)}),(t,o)=>{const f=ye,g=G,C=ee;return a(),l("div",be,[n("div",xe,[n("div",ke,[n("div",null,[(a(),l("span",$e,"Current route"))]),v(e(O),{modelValue:e(r),"onUpdate:modelValue":o[0]||(o[0]=x=>Z(r)?r.value=x:null),"left-icon":"i-carbon-direction-right-01 scale-y--100",class:E(e(i)?.path===e(r)?"":e(m).length?"text-green!":"text-orange!"),onKeydown:X(s,["enter"])},null,8,["modelValue","class"]),n("div",null,[e(i)?.path!==e(r)?(a(),l($,{key:0},[o[6]||(o[6]=n("span",null,[V("Press "),n("b",{"font-bold":""},"Enter"),V(" to navigate")],-1)),e(m).length?_("",!0):(a(),l("span",we," (no match)"))],64)):(a(),l("span",Re,"Edit path above to navigate"))])]),v(e(q),{class:"of-hidden"},{default:y(()=>[v(e(S),{size:"70",class:"of-auto!"},{default:y(()=>[v(g,{icon:"i-carbon-tree-view-alt",text:"All Routes",description:`${e(d).length} routes registered in your application`,padding:!1},{default:y(()=>[e(d).length?(a(),w(f,{key:0,pages:e(d),matched:e(i)?.matched??[],"matched-pending":e(m),onNavigate:p,onSelectMeta:o[1]||(o[1]=x=>c.value=x)},null,8,["pages","matched","matched-pending"])):_("",!0)]),_:1},8,["description"])]),_:1}),e(c)?(a(),w(e(S),{key:0,size:"30",class:"of-auto!"},{default:y(()=>[v(C,{meta:e(c),onClose:o[2]||(o[2]=x=>c.value=void 0)},null,8,["meta"])]),_:1})):_("",!0)]),_:1})])])}}});export{Ne as default};
