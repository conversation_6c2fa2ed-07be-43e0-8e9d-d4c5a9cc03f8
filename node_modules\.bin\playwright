#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/test/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/test/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules/@playwright/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/@playwright+test@1.54.1/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@playwright/test/cli.js" "$@"
else
  exec node  "$basedir/../@playwright/test/cli.js" "$@"
fi
