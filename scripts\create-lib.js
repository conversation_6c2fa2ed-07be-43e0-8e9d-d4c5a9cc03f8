#!/usr/bin/env node

/**
 * 创建新的公共库脚本
 * 使用方法: node scripts/create-lib.js <lib-name>
 */

const fs = require('fs');
const path = require('path');

const libName = process.argv[2];
if (!libName) {
  console.error('请提供库名称: node scripts/create-lib.js <lib-name>');
  process.exit(1);
}

const libDir = path.join(__dirname, '..', 'package', libName);

// 创建目录结构
if (!fs.existsSync(libDir)) {
  fs.mkdirSync(libDir, { recursive: true });
}

const srcDir = path.join(libDir, 'src');
if (!fs.existsSync(srcDir)) {
  fs.mkdirSync(srcDir);
}

// 创建 package.json
const packageJson = {
  name: `@libc/${libName}`,
  version: "1.0.0",
  description: "",
  main: "dist/index.js",
  types: "dist/index.d.ts",
  scripts: {
    build: "tsc",
    dev: "tsc --watch",
    test: "echo \"Error: no test specified\" && exit 1"
  },
  devDependencies: {
    typescript: "~5.8.0"
  },
  keywords: [],
  author: "",
  license: "ISC",
  packageManager: "pnpm@10.13.1"
};

fs.writeFileSync(
  path.join(libDir, 'package.json'),
  JSON.stringify(packageJson, null, 2)
);

// 创建 tsconfig.json
const tsConfig = {
  compilerOptions: {
    target: "ES2020",
    module: "ESNext",
    moduleResolution: "node",
    declaration: true,
    outDir: "dist",
    strict: true,
    esModuleInterop: true,
    skipLibCheck: true,
    forceConsistentCasingInFileNames: true
  },
  include: ["src/**/*"],
  exclude: ["node_modules", "dist"]
};

fs.writeFileSync(
  path.join(libDir, 'tsconfig.json'),
  JSON.stringify(tsConfig, null, 2)
);

// 创建示例 index.ts
const indexTs = `/**
 * ${libName} 公共库
 */

// 示例函数
export const hello = (name: string): string => {
  return \`Hello, \${name}!\`;
};

// 添加更多函数...
`;

fs.writeFileSync(path.join(srcDir, 'index.ts'), indexTs);

console.log(`✅ 公共库 @libc/${libName} 创建成功!`);
console.log(`📁 位置: ${libDir}`);
console.log(`🔧 下一步:`);
console.log(`   1. cd ${libDir}`);
console.log(`   2. pnpm install`);
console.log(`   3. pnpm build`);
