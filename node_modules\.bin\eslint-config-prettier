#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules:/mnt/d/前端code/微前端/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/cli.js" "$@"
else
  exec node  "$basedir/../.pnpm/eslint-config-prettier@10.1.8_eslint@9.32.0_jiti@2.5.1_/node_modules/eslint-config-prettier/bin/cli.js" "$@"
fi
