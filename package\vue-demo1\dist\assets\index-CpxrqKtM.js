(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ts(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const X={},bt=[],je=()=>{},Ui=()=>!1,pn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ns=e=>e.startsWith("onUpdate:"),oe=Object.assign,ss=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Vi=Object.prototype.hasOwnProperty,k=(e,t)=>Vi.call(e,t),H=Array.isArray,vt=e=>gn(e)==="[object Map]",vr=e=>gn(e)==="[object Set]",D=e=>typeof e=="function",te=e=>typeof e=="string",st=e=>typeof e=="symbol",ee=e=>e!==null&&typeof e=="object",xr=e=>(ee(e)||D(e))&&D(e.then)&&D(e.catch),Er=Object.prototype.toString,gn=e=>Er.call(e),Ki=e=>gn(e).slice(8,-1),Sr=e=>gn(e)==="[object Object]",rs=e=>te(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ft=ts(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),mn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Wi=/-(\w)/g,nt=mn(e=>e.replace(Wi,(t,n)=>n?n.toUpperCase():"")),ki=/\B([A-Z])/g,at=mn(e=>e.replace(ki,"-$1").toLowerCase()),wr=mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),wn=mn(e=>e?`on${wr(e)}`:""),tt=(e,t)=>!Object.is(e,t),Rn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Dn=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},qi=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Os;const _n=()=>Os||(Os=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function is(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=te(s)?Qi(s):is(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(te(e)||ee(e))return e}const Gi=/;(?![^(]*\))/g,zi=/:([^]+)/,Yi=/\/\*[^]*?\*\//g;function Qi(e){const t={};return e.replace(Yi,"").split(Gi).forEach(n=>{if(n){const s=n.split(zi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function os(e){let t="";if(te(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=os(e[n]);s&&(t+=s+" ")}else if(ee(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ji="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Xi=ts(Ji);function Rr(e){return!!e||e===""}const Cr=e=>!!(e&&e.__v_isRef===!0),Pr=e=>te(e)?e:e==null?"":H(e)||ee(e)&&(e.toString===Er||!D(e.toString))?Cr(e)?Pr(e.value):JSON.stringify(e,Or,2):String(e),Or=(e,t)=>Cr(t)?Or(e,t.value):vt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Cn(s,i)+" =>"]=r,n),{})}:vr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Cn(n))}:st(t)?Cn(t):ee(t)&&!H(t)&&!Sr(t)?String(t):t,Cn=(e,t="")=>{var n;return st(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let pe;class Ar{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=pe,!t&&pe&&(this.index=(pe.scopes||(pe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=pe;try{return pe=this,t()}finally{pe=n}}}on(){++this._on===1&&(this.prevScope=pe,pe=this)}off(){this._on>0&&--this._on===0&&(pe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Zi(e){return new Ar(e)}function eo(){return pe}let J;const Pn=new WeakSet;class Tr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,pe&&pe.active&&pe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Pn.has(this)&&(Pn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Mr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,As(this),Fr(this);const t=J,n=xe;J=this,xe=!0;try{return this.fn()}finally{Nr(this),J=t,xe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)fs(t);this.deps=this.depsTail=void 0,As(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Pn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Bn(this)&&this.run()}get dirty(){return Bn(this)}}let Ir=0,Nt,$t;function Mr(e,t=!1){if(e.flags|=8,t){e.next=$t,$t=e;return}e.next=Nt,Nt=e}function ls(){Ir++}function cs(){if(--Ir>0)return;if($t){let t=$t;for($t=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Nt;){let t=Nt;for(Nt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Fr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Nr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),fs(s),to(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Bn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&($r(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function $r(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Kt)||(e.globalVersion=Kt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Bn(e))))return;e.flags|=2;const t=e.dep,n=J,s=xe;J=e,xe=!0;try{Fr(e);const r=e.fn(e._value);(t.version===0||tt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{J=n,xe=s,Nr(e),e.flags&=-3}}function fs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)fs(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function to(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let xe=!0;const jr=[];function We(){jr.push(xe),xe=!1}function ke(){const e=jr.pop();xe=e===void 0?!0:e}function As(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=J;J=void 0;try{t()}finally{J=n}}}let Kt=0;class no{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class us{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!J||!xe||J===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==J)n=this.activeLink=new no(J,this),J.deps?(n.prevDep=J.depsTail,J.depsTail.nextDep=n,J.depsTail=n):J.deps=J.depsTail=n,Lr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=J.depsTail,n.nextDep=void 0,J.depsTail.nextDep=n,J.depsTail=n,J.deps===n&&(J.deps=s)}return n}trigger(t){this.version++,Kt++,this.notify(t)}notify(t){ls();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{cs()}}}function Lr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Lr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Un=new WeakMap,ct=Symbol(""),Vn=Symbol(""),Wt=Symbol("");function re(e,t,n){if(xe&&J){let s=Un.get(e);s||Un.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new us),r.map=s,r.key=n),r.track()}}function Ve(e,t,n,s,r,i){const o=Un.get(e);if(!o){Kt++;return}const c=l=>{l&&l.trigger()};if(ls(),t==="clear")o.forEach(c);else{const l=H(e),d=l&&rs(n);if(l&&n==="length"){const a=Number(s);o.forEach((h,g)=>{(g==="length"||g===Wt||!st(g)&&g>=a)&&c(h)})}else switch((n!==void 0||o.has(void 0))&&c(o.get(n)),d&&c(o.get(Wt)),t){case"add":l?d&&c(o.get("length")):(c(o.get(ct)),vt(e)&&c(o.get(Vn)));break;case"delete":l||(c(o.get(ct)),vt(e)&&c(o.get(Vn)));break;case"set":vt(e)&&c(o.get(ct));break}}cs()}function gt(e){const t=W(e);return t===e?t:(re(t,"iterate",Wt),Ee(e)?t:t.map(ce))}function as(e){return re(e=W(e),"iterate",Wt),e}const so={__proto__:null,[Symbol.iterator](){return On(this,Symbol.iterator,ce)},concat(...e){return gt(this).concat(...e.map(t=>H(t)?gt(t):t))},entries(){return On(this,"entries",e=>(e[1]=ce(e[1]),e))},every(e,t){return De(this,"every",e,t,void 0,arguments)},filter(e,t){return De(this,"filter",e,t,n=>n.map(ce),arguments)},find(e,t){return De(this,"find",e,t,ce,arguments)},findIndex(e,t){return De(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return De(this,"findLast",e,t,ce,arguments)},findLastIndex(e,t){return De(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return De(this,"forEach",e,t,void 0,arguments)},includes(...e){return An(this,"includes",e)},indexOf(...e){return An(this,"indexOf",e)},join(e){return gt(this).join(e)},lastIndexOf(...e){return An(this,"lastIndexOf",e)},map(e,t){return De(this,"map",e,t,void 0,arguments)},pop(){return At(this,"pop")},push(...e){return At(this,"push",e)},reduce(e,...t){return Ts(this,"reduce",e,t)},reduceRight(e,...t){return Ts(this,"reduceRight",e,t)},shift(){return At(this,"shift")},some(e,t){return De(this,"some",e,t,void 0,arguments)},splice(...e){return At(this,"splice",e)},toReversed(){return gt(this).toReversed()},toSorted(e){return gt(this).toSorted(e)},toSpliced(...e){return gt(this).toSpliced(...e)},unshift(...e){return At(this,"unshift",e)},values(){return On(this,"values",ce)}};function On(e,t,n){const s=as(e),r=s[t]();return s!==e&&!Ee(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const ro=Array.prototype;function De(e,t,n,s,r,i){const o=as(e),c=o!==e&&!Ee(e),l=o[t];if(l!==ro[t]){const h=l.apply(e,i);return c?ce(h):h}let d=n;o!==e&&(c?d=function(h,g){return n.call(this,ce(h),g,e)}:n.length>2&&(d=function(h,g){return n.call(this,h,g,e)}));const a=l.call(o,d,s);return c&&r?r(a):a}function Ts(e,t,n,s){const r=as(e);let i=n;return r!==e&&(Ee(e)?n.length>3&&(i=function(o,c,l){return n.call(this,o,c,l,e)}):i=function(o,c,l){return n.call(this,o,ce(c),l,e)}),r[t](i,...s)}function An(e,t,n){const s=W(e);re(s,"iterate",Wt);const r=s[t](...n);return(r===-1||r===!1)&&ps(n[0])?(n[0]=W(n[0]),s[t](...n)):r}function At(e,t,n=[]){We(),ls();const s=W(e)[t].apply(e,n);return cs(),ke(),s}const io=ts("__proto__,__v_isRef,__isVue"),Hr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(st));function oo(e){st(e)||(e=String(e));const t=W(this);return re(t,"has",e),t.hasOwnProperty(e)}class Dr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?_o:Kr:i?Vr:Ur).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=H(t);if(!r){let l;if(o&&(l=so[n]))return l;if(n==="hasOwnProperty")return oo}const c=Reflect.get(t,n,ie(t)?t:s);return(st(n)?Hr.has(n):io(n))||(r||re(t,"get",n),i)?c:ie(c)?o&&rs(n)?c:c.value:ee(c)?r?kr(c):yn(c):c}}class Br extends Dr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const l=ut(i);if(!Ee(s)&&!ut(s)&&(i=W(i),s=W(s)),!H(t)&&ie(i)&&!ie(s))return l?!1:(i.value=s,!0)}const o=H(t)&&rs(n)?Number(n)<t.length:k(t,n),c=Reflect.set(t,n,s,ie(t)?t:r);return t===W(r)&&(o?tt(s,i)&&Ve(t,"set",n,s):Ve(t,"add",n,s)),c}deleteProperty(t,n){const s=k(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ve(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!st(n)||!Hr.has(n))&&re(t,"has",n),s}ownKeys(t){return re(t,"iterate",H(t)?"length":ct),Reflect.ownKeys(t)}}class lo extends Dr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const co=new Br,fo=new lo,uo=new Br(!0);const Kn=e=>e,Zt=e=>Reflect.getPrototypeOf(e);function ao(e,t,n){return function(...s){const r=this.__v_raw,i=W(r),o=vt(i),c=e==="entries"||e===Symbol.iterator&&o,l=e==="keys"&&o,d=r[e](...s),a=n?Kn:t?Wn:ce;return!t&&re(i,"iterate",l?Vn:ct),{next(){const{value:h,done:g}=d.next();return g?{value:h,done:g}:{value:c?[a(h[0]),a(h[1])]:a(h),done:g}},[Symbol.iterator](){return this}}}}function en(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ho(e,t){const n={get(r){const i=this.__v_raw,o=W(i),c=W(r);e||(tt(r,c)&&re(o,"get",r),re(o,"get",c));const{has:l}=Zt(o),d=t?Kn:e?Wn:ce;if(l.call(o,r))return d(i.get(r));if(l.call(o,c))return d(i.get(c));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&re(W(r),"iterate",ct),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=W(i),c=W(r);return e||(tt(r,c)&&re(o,"has",r),re(o,"has",c)),r===c?i.has(r):i.has(r)||i.has(c)},forEach(r,i){const o=this,c=o.__v_raw,l=W(c),d=t?Kn:e?Wn:ce;return!e&&re(l,"iterate",ct),c.forEach((a,h)=>r.call(i,d(a),d(h),o))}};return oe(n,e?{add:en("add"),set:en("set"),delete:en("delete"),clear:en("clear")}:{add(r){!t&&!Ee(r)&&!ut(r)&&(r=W(r));const i=W(this);return Zt(i).has.call(i,r)||(i.add(r),Ve(i,"add",r,r)),this},set(r,i){!t&&!Ee(i)&&!ut(i)&&(i=W(i));const o=W(this),{has:c,get:l}=Zt(o);let d=c.call(o,r);d||(r=W(r),d=c.call(o,r));const a=l.call(o,r);return o.set(r,i),d?tt(i,a)&&Ve(o,"set",r,i):Ve(o,"add",r,i),this},delete(r){const i=W(this),{has:o,get:c}=Zt(i);let l=o.call(i,r);l||(r=W(r),l=o.call(i,r)),c&&c.call(i,r);const d=i.delete(r);return l&&Ve(i,"delete",r,void 0),d},clear(){const r=W(this),i=r.size!==0,o=r.clear();return i&&Ve(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ao(r,e,t)}),n}function hs(e,t){const n=ho(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(k(n,r)&&r in s?n:s,r,i)}const po={get:hs(!1,!1)},go={get:hs(!1,!0)},mo={get:hs(!0,!1)};const Ur=new WeakMap,Vr=new WeakMap,Kr=new WeakMap,_o=new WeakMap;function yo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function bo(e){return e.__v_skip||!Object.isExtensible(e)?0:yo(Ki(e))}function yn(e){return ut(e)?e:ds(e,!1,co,po,Ur)}function Wr(e){return ds(e,!1,uo,go,Vr)}function kr(e){return ds(e,!0,fo,mo,Kr)}function ds(e,t,n,s,r){if(!ee(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=bo(e);if(i===0)return e;const o=r.get(e);if(o)return o;const c=new Proxy(e,i===2?s:n);return r.set(e,c),c}function jt(e){return ut(e)?jt(e.__v_raw):!!(e&&e.__v_isReactive)}function ut(e){return!!(e&&e.__v_isReadonly)}function Ee(e){return!!(e&&e.__v_isShallow)}function ps(e){return e?!!e.__v_raw:!1}function W(e){const t=e&&e.__v_raw;return t?W(t):e}function qr(e){return!k(e,"__v_skip")&&Object.isExtensible(e)&&Dn(e,"__v_skip",!0),e}const ce=e=>ee(e)?yn(e):e,Wn=e=>ee(e)?kr(e):e;function ie(e){return e?e.__v_isRef===!0:!1}function Gr(e){return zr(e,!1)}function vo(e){return zr(e,!0)}function zr(e,t){return ie(e)?e:new xo(e,t)}class xo{constructor(t,n){this.dep=new us,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:W(t),this._value=n?t:ce(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ee(t)||ut(t);t=s?t:W(t),tt(t,n)&&(this._rawValue=t,this._value=s?t:ce(t),this.dep.trigger())}}function ft(e){return ie(e)?e.value:e}const Eo={get:(e,t,n)=>t==="__v_raw"?e:ft(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ie(r)&&!ie(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Yr(e){return jt(e)?e:new Proxy(e,Eo)}class So{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new us(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Kt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&J!==this)return Mr(this,!0),!0}get value(){const t=this.dep.track();return $r(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function wo(e,t,n=!1){let s,r;return D(e)?s=e:(s=e.get,r=e.set),new So(s,r,n)}const tn={},cn=new WeakMap;let lt;function Ro(e,t=!1,n=lt){if(n){let s=cn.get(n);s||cn.set(n,s=[]),s.push(e)}}function Co(e,t,n=X){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:c,call:l}=n,d=T=>r?T:Ee(T)||r===!1||r===0?et(T,1):et(T);let a,h,g,m,O=!1,A=!1;if(ie(e)?(h=()=>e.value,O=Ee(e)):jt(e)?(h=()=>d(e),O=!0):H(e)?(A=!0,O=e.some(T=>jt(T)||Ee(T)),h=()=>e.map(T=>{if(ie(T))return T.value;if(jt(T))return d(T);if(D(T))return l?l(T,2):T()})):D(e)?t?h=l?()=>l(e,2):e:h=()=>{if(g){We();try{g()}finally{ke()}}const T=lt;lt=a;try{return l?l(e,3,[m]):e(m)}finally{lt=T}}:h=je,t&&r){const T=h,z=r===!0?1/0:r;h=()=>et(T(),z)}const B=eo(),$=()=>{a.stop(),B&&B.active&&ss(B.effects,a)};if(i&&t){const T=t;t=(...z)=>{T(...z),$()}}let M=A?new Array(e.length).fill(tn):tn;const j=T=>{if(!(!(a.flags&1)||!a.dirty&&!T))if(t){const z=a.run();if(r||O||(A?z.some((se,Z)=>tt(se,M[Z])):tt(z,M))){g&&g();const se=lt;lt=a;try{const Z=[z,M===tn?void 0:A&&M[0]===tn?[]:M,m];M=z,l?l(t,3,Z):t(...Z)}finally{lt=se}}}else a.run()};return c&&c(j),a=new Tr(h),a.scheduler=o?()=>o(j,!1):j,m=T=>Ro(T,!1,a),g=a.onStop=()=>{const T=cn.get(a);if(T){if(l)l(T,4);else for(const z of T)z();cn.delete(a)}},t?s?j(!0):M=a.run():o?o(j.bind(null,!0),!0):a.run(),$.pause=a.pause.bind(a),$.resume=a.resume.bind(a),$.stop=$,$}function et(e,t=1/0,n){if(t<=0||!ee(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ie(e))et(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)et(e[s],t,n);else if(vr(e)||vt(e))e.forEach(s=>{et(s,t,n)});else if(Sr(e)){for(const s in e)et(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&et(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Qt(e,t,n,s){try{return s?e(...s):e()}catch(r){bn(r,t,n)}}function Le(e,t,n,s){if(D(e)){const r=Qt(e,t,n,s);return r&&xr(r)&&r.catch(i=>{bn(i,t,n)}),r}if(H(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Le(e[i],t,n,s));return r}}function bn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||X;if(t){let c=t.parent;const l=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;c;){const a=c.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,l,d)===!1)return}c=c.parent}if(i){We(),Qt(i,null,10,[e,l,d]),ke();return}}Po(e,n,r,s,o)}function Po(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const fe=[];let Me=-1;const xt=[];let Je=null,mt=0;const Qr=Promise.resolve();let fn=null;function Jr(e){const t=fn||Qr;return e?t.then(this?e.bind(this):e):t}function Oo(e){let t=Me+1,n=fe.length;for(;t<n;){const s=t+n>>>1,r=fe[s],i=kt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function gs(e){if(!(e.flags&1)){const t=kt(e),n=fe[fe.length-1];!n||!(e.flags&2)&&t>=kt(n)?fe.push(e):fe.splice(Oo(t),0,e),e.flags|=1,Xr()}}function Xr(){fn||(fn=Qr.then(ei))}function Ao(e){H(e)?xt.push(...e):Je&&e.id===-1?Je.splice(mt+1,0,e):e.flags&1||(xt.push(e),e.flags|=1),Xr()}function Is(e,t,n=Me+1){for(;n<fe.length;n++){const s=fe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;fe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Zr(e){if(xt.length){const t=[...new Set(xt)].sort((n,s)=>kt(n)-kt(s));if(xt.length=0,Je){Je.push(...t);return}for(Je=t,mt=0;mt<Je.length;mt++){const n=Je[mt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Je=null,mt=0}}const kt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ei(e){try{for(Me=0;Me<fe.length;Me++){const t=fe[Me];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Qt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Me<fe.length;Me++){const t=fe[Me];t&&(t.flags&=-2)}Me=-1,fe.length=0,Zr(),fn=null,(fe.length||xt.length)&&ei()}}let $e=null,ti=null;function un(e){const t=$e;return $e=e,ti=e&&e.type.__scopeId||null,t}function To(e,t=$e,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Bs(-1);const i=un(t);let o;try{o=e(...r)}finally{un(i),s._d&&Bs(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function it(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const c=r[o];i&&(c.oldValue=i[o].value);let l=c.dir[s];l&&(We(),Le(l,n,8,[e.el,c,e,t]),ke())}}const Io=Symbol("_vte"),Mo=e=>e.__isTeleport;function ms(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ms(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function _s(e,t){return D(e)?oe({name:e.name},t,{setup:e}):e}function ni(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Lt(e,t,n,s,r=!1){if(H(e)){e.forEach((O,A)=>Lt(O,t&&(H(t)?t[A]:t),n,s,r));return}if(Ht(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Lt(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?xs(s.component):s.el,o=r?null:i,{i:c,r:l}=e,d=t&&t.r,a=c.refs===X?c.refs={}:c.refs,h=c.setupState,g=W(h),m=h===X?()=>!1:O=>k(g,O);if(d!=null&&d!==l&&(te(d)?(a[d]=null,m(d)&&(h[d]=null)):ie(d)&&(d.value=null)),D(l))Qt(l,c,12,[o,a]);else{const O=te(l),A=ie(l);if(O||A){const B=()=>{if(e.f){const $=O?m(l)?h[l]:a[l]:l.value;r?H($)&&ss($,i):H($)?$.includes(i)||$.push(i):O?(a[l]=[i],m(l)&&(h[l]=a[l])):(l.value=[i],e.k&&(a[e.k]=l.value))}else O?(a[l]=o,m(l)&&(h[l]=o)):A&&(l.value=o,e.k&&(a[e.k]=o))};o?(B.id=-1,me(B,n)):B()}}}_n().requestIdleCallback;_n().cancelIdleCallback;const Ht=e=>!!e.type.__asyncLoader,si=e=>e.type.__isKeepAlive;function Fo(e,t){ri(e,"a",t)}function No(e,t){ri(e,"da",t)}function ri(e,t,n=ue){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(vn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)si(r.parent.vnode)&&$o(s,t,n,r),r=r.parent}}function $o(e,t,n,s){const r=vn(t,e,s,!0);ii(()=>{ss(s[t],r)},n)}function vn(e,t,n=ue,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{We();const c=Jt(n),l=Le(t,n,e,o);return c(),ke(),l});return s?r.unshift(i):r.push(i),i}}const qe=e=>(t,n=ue)=>{(!Gt||e==="sp")&&vn(e,(...s)=>t(...s),n)},jo=qe("bm"),Lo=qe("m"),Ho=qe("bu"),Do=qe("u"),Bo=qe("bum"),ii=qe("um"),Uo=qe("sp"),Vo=qe("rtg"),Ko=qe("rtc");function Wo(e,t=ue){vn("ec",e,t)}const ko=Symbol.for("v-ndc"),kn=e=>e?wi(e)?xs(e):kn(e.parent):null,Dt=oe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>kn(e.parent),$root:e=>kn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>li(e),$forceUpdate:e=>e.f||(e.f=()=>{gs(e.update)}),$nextTick:e=>e.n||(e.n=Jr.bind(e.proxy)),$watch:e=>hl.bind(e)}),Tn=(e,t)=>e!==X&&!e.__isScriptSetup&&k(e,t),qo={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:c,appContext:l}=e;let d;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Tn(s,t))return o[t]=1,s[t];if(r!==X&&k(r,t))return o[t]=2,r[t];if((d=e.propsOptions[0])&&k(d,t))return o[t]=3,i[t];if(n!==X&&k(n,t))return o[t]=4,n[t];qn&&(o[t]=0)}}const a=Dt[t];let h,g;if(a)return t==="$attrs"&&re(e.attrs,"get",""),a(e);if((h=c.__cssModules)&&(h=h[t]))return h;if(n!==X&&k(n,t))return o[t]=4,n[t];if(g=l.config.globalProperties,k(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Tn(r,t)?(r[t]=n,!0):s!==X&&k(s,t)?(s[t]=n,!0):k(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let c;return!!n[o]||e!==X&&k(e,o)||Tn(t,o)||(c=i[0])&&k(c,o)||k(s,o)||k(Dt,o)||k(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:k(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ms(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let qn=!0;function Go(e){const t=li(e),n=e.proxy,s=e.ctx;qn=!1,t.beforeCreate&&Fs(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:c,provide:l,inject:d,created:a,beforeMount:h,mounted:g,beforeUpdate:m,updated:O,activated:A,deactivated:B,beforeDestroy:$,beforeUnmount:M,destroyed:j,unmounted:T,render:z,renderTracked:se,renderTriggered:Z,errorCaptured:we,serverPrefetch:Ge,expose:Re,inheritAttrs:ze,components:rt,directives:Ce,filters:Pt}=t;if(d&&zo(d,s,null),o)for(const G in o){const V=o[G];D(V)&&(s[G]=V.bind(n))}if(r){const G=r.call(n,n);ee(G)&&(e.data=yn(G))}if(qn=!0,i)for(const G in i){const V=i[G],He=D(V)?V.bind(n,n):D(V.get)?V.get.bind(n,n):je,Ye=!D(V)&&D(V.set)?V.set.bind(n):je,Pe=be({get:He,set:Ye});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Pe.value,set:ae=>Pe.value=ae})}if(c)for(const G in c)oi(c[G],s,n,G);if(l){const G=D(l)?l.call(n):l;Reflect.ownKeys(G).forEach(V=>{sn(V,G[V])})}a&&Fs(a,e,"c");function ne(G,V){H(V)?V.forEach(He=>G(He.bind(n))):V&&G(V.bind(n))}if(ne(jo,h),ne(Lo,g),ne(Ho,m),ne(Do,O),ne(Fo,A),ne(No,B),ne(Wo,we),ne(Ko,se),ne(Vo,Z),ne(Bo,M),ne(ii,T),ne(Uo,Ge),H(Re))if(Re.length){const G=e.exposed||(e.exposed={});Re.forEach(V=>{Object.defineProperty(G,V,{get:()=>n[V],set:He=>n[V]=He,enumerable:!0})})}else e.exposed||(e.exposed={});z&&e.render===je&&(e.render=z),ze!=null&&(e.inheritAttrs=ze),rt&&(e.components=rt),Ce&&(e.directives=Ce),Ge&&ni(e)}function zo(e,t,n=je){H(e)&&(e=Gn(e));for(const s in e){const r=e[s];let i;ee(r)?"default"in r?i=Ke(r.from||s,r.default,!0):i=Ke(r.from||s):i=Ke(r),ie(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Fs(e,t,n){Le(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function oi(e,t,n,s){let r=s.includes(".")?vi(n,s):()=>n[s];if(te(e)){const i=t[e];D(i)&&rn(r,i)}else if(D(e))rn(r,e.bind(n));else if(ee(e))if(H(e))e.forEach(i=>oi(i,t,n,s));else{const i=D(e.handler)?e.handler.bind(n):t[e.handler];D(i)&&rn(r,i,e)}}function li(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,c=i.get(t);let l;return c?l=c:!r.length&&!n&&!s?l=t:(l={},r.length&&r.forEach(d=>an(l,d,o,!0)),an(l,t,o)),ee(t)&&i.set(t,l),l}function an(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&an(e,i,n,!0),r&&r.forEach(o=>an(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const c=Yo[o]||n&&n[o];e[o]=c?c(e[o],t[o]):t[o]}return e}const Yo={data:Ns,props:$s,emits:$s,methods:Mt,computed:Mt,beforeCreate:le,created:le,beforeMount:le,mounted:le,beforeUpdate:le,updated:le,beforeDestroy:le,beforeUnmount:le,destroyed:le,unmounted:le,activated:le,deactivated:le,errorCaptured:le,serverPrefetch:le,components:Mt,directives:Mt,watch:Jo,provide:Ns,inject:Qo};function Ns(e,t){return t?e?function(){return oe(D(e)?e.call(this,this):e,D(t)?t.call(this,this):t)}:t:e}function Qo(e,t){return Mt(Gn(e),Gn(t))}function Gn(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function le(e,t){return e?[...new Set([].concat(e,t))]:t}function Mt(e,t){return e?oe(Object.create(null),e,t):t}function $s(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:oe(Object.create(null),Ms(e),Ms(t??{})):t}function Jo(e,t){if(!e)return t;if(!t)return e;const n=oe(Object.create(null),e);for(const s in t)n[s]=le(e[s],t[s]);return n}function ci(){return{app:null,config:{isNativeTag:Ui,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Xo=0;function Zo(e,t){return function(s,r=null){D(s)||(s=oe({},s)),r!=null&&!ee(r)&&(r=null);const i=ci(),o=new WeakSet,c=[];let l=!1;const d=i.app={_uid:Xo++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:jl,get config(){return i.config},set config(a){},use(a,...h){return o.has(a)||(a&&D(a.install)?(o.add(a),a.install(d,...h)):D(a)&&(o.add(a),a(d,...h))),d},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),d},component(a,h){return h?(i.components[a]=h,d):i.components[a]},directive(a,h){return h?(i.directives[a]=h,d):i.directives[a]},mount(a,h,g){if(!l){const m=d._ceVNode||ve(s,r);return m.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(m,a,g),l=!0,d._container=a,a.__vue_app__=d,xs(m.component)}},onUnmount(a){c.push(a)},unmount(){l&&(Le(c,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(a,h){return i.provides[a]=h,d},runWithContext(a){const h=Et;Et=d;try{return a()}finally{Et=h}}};return d}}let Et=null;function sn(e,t){if(ue){let n=ue.provides;const s=ue.parent&&ue.parent.provides;s===n&&(n=ue.provides=Object.create(s)),n[e]=t}}function Ke(e,t,n=!1){const s=Tl();if(s||Et){let r=Et?Et._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&D(t)?t.call(s&&s.proxy):t}}const fi={},ui=()=>Object.create(fi),ai=e=>Object.getPrototypeOf(e)===fi;function el(e,t,n,s=!1){const r={},i=ui();e.propsDefaults=Object.create(null),hi(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Wr(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function tl(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,c=W(r),[l]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let g=a[h];if(xn(e.emitsOptions,g))continue;const m=t[g];if(l)if(k(i,g))m!==i[g]&&(i[g]=m,d=!0);else{const O=nt(g);r[O]=zn(l,c,O,m,e,!1)}else m!==i[g]&&(i[g]=m,d=!0)}}}else{hi(e,t,r,i)&&(d=!0);let a;for(const h in c)(!t||!k(t,h)&&((a=at(h))===h||!k(t,a)))&&(l?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=zn(l,c,h,void 0,e,!0)):delete r[h]);if(i!==c)for(const h in i)(!t||!k(t,h))&&(delete i[h],d=!0)}d&&Ve(e.attrs,"set","")}function hi(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,c;if(t)for(let l in t){if(Ft(l))continue;const d=t[l];let a;r&&k(r,a=nt(l))?!i||!i.includes(a)?n[a]=d:(c||(c={}))[a]=d:xn(e.emitsOptions,l)||(!(l in s)||d!==s[l])&&(s[l]=d,o=!0)}if(i){const l=W(n),d=c||X;for(let a=0;a<i.length;a++){const h=i[a];n[h]=zn(r,l,h,d[h],e,!k(d,h))}}return o}function zn(e,t,n,s,r,i){const o=e[n];if(o!=null){const c=k(o,"default");if(c&&s===void 0){const l=o.default;if(o.type!==Function&&!o.skipFactory&&D(l)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const a=Jt(r);s=d[n]=l.call(null,t),a()}}else s=l;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!c?s=!1:o[1]&&(s===""||s===at(n))&&(s=!0))}return s}const nl=new WeakMap;function di(e,t,n=!1){const s=n?nl:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},c=[];let l=!1;if(!D(e)){const a=h=>{l=!0;const[g,m]=di(h,t,!0);oe(o,g),m&&c.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!l)return ee(e)&&s.set(e,bt),bt;if(H(i))for(let a=0;a<i.length;a++){const h=nt(i[a]);js(h)&&(o[h]=X)}else if(i)for(const a in i){const h=nt(a);if(js(h)){const g=i[a],m=o[h]=H(g)||D(g)?{type:g}:oe({},g),O=m.type;let A=!1,B=!0;if(H(O))for(let $=0;$<O.length;++$){const M=O[$],j=D(M)&&M.name;if(j==="Boolean"){A=!0;break}else j==="String"&&(B=!1)}else A=D(O)&&O.name==="Boolean";m[0]=A,m[1]=B,(A||k(m,"default"))&&c.push(h)}}const d=[o,c];return ee(e)&&s.set(e,d),d}function js(e){return e[0]!=="$"&&!Ft(e)}const ys=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",bs=e=>H(e)?e.map(Ne):[Ne(e)],sl=(e,t,n)=>{if(t._n)return t;const s=To((...r)=>bs(t(...r)),n);return s._c=!1,s},pi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(ys(r))continue;const i=e[r];if(D(i))t[r]=sl(r,i,s);else if(i!=null){const o=bs(i);t[r]=()=>o}}},gi=(e,t)=>{const n=bs(t);e.slots.default=()=>n},mi=(e,t,n)=>{for(const s in t)(n||!ys(s))&&(e[s]=t[s])},rl=(e,t,n)=>{const s=e.slots=ui();if(e.vnode.shapeFlag&32){const r=t.__;r&&Dn(s,"__",r,!0);const i=t._;i?(mi(s,t,n),n&&Dn(s,"_",i,!0)):pi(t,s)}else t&&gi(e,t)},il=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=X;if(s.shapeFlag&32){const c=t._;c?n&&c===1?i=!1:mi(r,t,n):(i=!t.$stable,pi(t,r)),o=t}else t&&(gi(e,t),o={default:1});if(i)for(const c in r)!ys(c)&&o[c]==null&&delete r[c]},me=bl;function ol(e){return ll(e)}function ll(e,t){const n=_n();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:c,createComment:l,setText:d,setElementText:a,parentNode:h,nextSibling:g,setScopeId:m=je,insertStaticContent:O}=e,A=(f,u,p,_=null,v=null,b=null,w=void 0,S=null,E=!!u.dynamicChildren)=>{if(f===u)return;f&&!Tt(f,u)&&(_=y(f),ae(f,v,b,!0),f=null),u.patchFlag===-2&&(E=!1,u.dynamicChildren=null);const{type:x,ref:N,shapeFlag:C}=u;switch(x){case En:B(f,u,p,_);break;case St:$(f,u,p,_);break;case Mn:f==null&&M(u,p,_,w);break;case Fe:rt(f,u,p,_,v,b,w,S,E);break;default:C&1?z(f,u,p,_,v,b,w,S,E):C&6?Ce(f,u,p,_,v,b,w,S,E):(C&64||C&128)&&x.process(f,u,p,_,v,b,w,S,E,I)}N!=null&&v?Lt(N,f&&f.ref,b,u||f,!u):N==null&&f&&f.ref!=null&&Lt(f.ref,null,b,f,!0)},B=(f,u,p,_)=>{if(f==null)s(u.el=c(u.children),p,_);else{const v=u.el=f.el;u.children!==f.children&&d(v,u.children)}},$=(f,u,p,_)=>{f==null?s(u.el=l(u.children||""),p,_):u.el=f.el},M=(f,u,p,_)=>{[f.el,f.anchor]=O(f.children,u,p,_,f.el,f.anchor)},j=({el:f,anchor:u},p,_)=>{let v;for(;f&&f!==u;)v=g(f),s(f,p,_),f=v;s(u,p,_)},T=({el:f,anchor:u})=>{let p;for(;f&&f!==u;)p=g(f),r(f),f=p;r(u)},z=(f,u,p,_,v,b,w,S,E)=>{u.type==="svg"?w="svg":u.type==="math"&&(w="mathml"),f==null?se(u,p,_,v,b,w,S,E):Ge(f,u,v,b,w,S,E)},se=(f,u,p,_,v,b,w,S)=>{let E,x;const{props:N,shapeFlag:C,transition:F,dirs:L}=f;if(E=f.el=o(f.type,b,N&&N.is,N),C&8?a(E,f.children):C&16&&we(f.children,E,null,_,v,In(f,b),w,S),L&&it(f,null,_,"created"),Z(E,f,f.scopeId,w,_),N){for(const Q in N)Q!=="value"&&!Ft(Q)&&i(E,Q,null,N[Q],b,_);"value"in N&&i(E,"value",null,N.value,b),(x=N.onVnodeBeforeMount)&&Ie(x,_,f)}L&&it(f,null,_,"beforeMount");const U=cl(v,F);U&&F.beforeEnter(E),s(E,u,p),((x=N&&N.onVnodeMounted)||U||L)&&me(()=>{x&&Ie(x,_,f),U&&F.enter(E),L&&it(f,null,_,"mounted")},v)},Z=(f,u,p,_,v)=>{if(p&&m(f,p),_)for(let b=0;b<_.length;b++)m(f,_[b]);if(v){let b=v.subTree;if(u===b||Ei(b.type)&&(b.ssContent===u||b.ssFallback===u)){const w=v.vnode;Z(f,w,w.scopeId,w.slotScopeIds,v.parent)}}},we=(f,u,p,_,v,b,w,S,E=0)=>{for(let x=E;x<f.length;x++){const N=f[x]=S?Xe(f[x]):Ne(f[x]);A(null,N,u,p,_,v,b,w,S)}},Ge=(f,u,p,_,v,b,w)=>{const S=u.el=f.el;let{patchFlag:E,dynamicChildren:x,dirs:N}=u;E|=f.patchFlag&16;const C=f.props||X,F=u.props||X;let L;if(p&&ot(p,!1),(L=F.onVnodeBeforeUpdate)&&Ie(L,p,u,f),N&&it(u,f,p,"beforeUpdate"),p&&ot(p,!0),(C.innerHTML&&F.innerHTML==null||C.textContent&&F.textContent==null)&&a(S,""),x?Re(f.dynamicChildren,x,S,p,_,In(u,v),b):w||V(f,u,S,null,p,_,In(u,v),b,!1),E>0){if(E&16)ze(S,C,F,p,v);else if(E&2&&C.class!==F.class&&i(S,"class",null,F.class,v),E&4&&i(S,"style",C.style,F.style,v),E&8){const U=u.dynamicProps;for(let Q=0;Q<U.length;Q++){const q=U[Q],he=C[q],de=F[q];(de!==he||q==="value")&&i(S,q,he,de,v,p)}}E&1&&f.children!==u.children&&a(S,u.children)}else!w&&x==null&&ze(S,C,F,p,v);((L=F.onVnodeUpdated)||N)&&me(()=>{L&&Ie(L,p,u,f),N&&it(u,f,p,"updated")},_)},Re=(f,u,p,_,v,b,w)=>{for(let S=0;S<u.length;S++){const E=f[S],x=u[S],N=E.el&&(E.type===Fe||!Tt(E,x)||E.shapeFlag&198)?h(E.el):p;A(E,x,N,null,_,v,b,w,!0)}},ze=(f,u,p,_,v)=>{if(u!==p){if(u!==X)for(const b in u)!Ft(b)&&!(b in p)&&i(f,b,u[b],null,v,_);for(const b in p){if(Ft(b))continue;const w=p[b],S=u[b];w!==S&&b!=="value"&&i(f,b,S,w,v,_)}"value"in p&&i(f,"value",u.value,p.value,v)}},rt=(f,u,p,_,v,b,w,S,E)=>{const x=u.el=f?f.el:c(""),N=u.anchor=f?f.anchor:c("");let{patchFlag:C,dynamicChildren:F,slotScopeIds:L}=u;L&&(S=S?S.concat(L):L),f==null?(s(x,p,_),s(N,p,_),we(u.children||[],p,N,v,b,w,S,E)):C>0&&C&64&&F&&f.dynamicChildren?(Re(f.dynamicChildren,F,p,v,b,w,S),(u.key!=null||v&&u===v.subTree)&&_i(f,u,!0)):V(f,u,p,N,v,b,w,S,E)},Ce=(f,u,p,_,v,b,w,S,E)=>{u.slotScopeIds=S,f==null?u.shapeFlag&512?v.ctx.activate(u,p,_,w,E):Pt(u,p,_,v,b,w,E):ht(f,u,E)},Pt=(f,u,p,_,v,b,w)=>{const S=f.component=Al(f,_,v);if(si(f)&&(S.ctx.renderer=I),Il(S,!1,w),S.asyncDep){if(v&&v.registerDep(S,ne,w),!f.el){const E=S.subTree=ve(St);$(null,E,u,p),f.placeholder=E.el}}else ne(S,f,u,p,v,b,w)},ht=(f,u,p)=>{const _=u.component=f.component;if(_l(f,u,p))if(_.asyncDep&&!_.asyncResolved){G(_,u,p);return}else _.next=u,_.update();else u.el=f.el,_.vnode=u},ne=(f,u,p,_,v,b,w)=>{const S=()=>{if(f.isMounted){let{next:C,bu:F,u:L,parent:U,vnode:Q}=f;{const Ae=yi(f);if(Ae){C&&(C.el=Q.el,G(f,C,w)),Ae.asyncDep.then(()=>{f.isUnmounted||S()});return}}let q=C,he;ot(f,!1),C?(C.el=Q.el,G(f,C,w)):C=Q,F&&Rn(F),(he=C.props&&C.props.onVnodeBeforeUpdate)&&Ie(he,U,C,Q),ot(f,!0);const de=Hs(f),Oe=f.subTree;f.subTree=de,A(Oe,de,h(Oe.el),y(Oe),f,v,b),C.el=de.el,q===null&&yl(f,de.el),L&&me(L,v),(he=C.props&&C.props.onVnodeUpdated)&&me(()=>Ie(he,U,C,Q),v)}else{let C;const{el:F,props:L}=u,{bm:U,m:Q,parent:q,root:he,type:de}=f,Oe=Ht(u);ot(f,!1),U&&Rn(U),!Oe&&(C=L&&L.onVnodeBeforeMount)&&Ie(C,q,u),ot(f,!0);{he.ce&&he.ce._def.shadowRoot!==!1&&he.ce._injectChildStyle(de);const Ae=f.subTree=Hs(f);A(null,Ae,p,_,f,v,b),u.el=Ae.el}if(Q&&me(Q,v),!Oe&&(C=L&&L.onVnodeMounted)){const Ae=u;me(()=>Ie(C,q,Ae),v)}(u.shapeFlag&256||q&&Ht(q.vnode)&&q.vnode.shapeFlag&256)&&f.a&&me(f.a,v),f.isMounted=!0,u=p=_=null}};f.scope.on();const E=f.effect=new Tr(S);f.scope.off();const x=f.update=E.run.bind(E),N=f.job=E.runIfDirty.bind(E);N.i=f,N.id=f.uid,E.scheduler=()=>gs(N),ot(f,!0),x()},G=(f,u,p)=>{u.component=f;const _=f.vnode.props;f.vnode=u,f.next=null,tl(f,u.props,_,p),il(f,u.children,p),We(),Is(f),ke()},V=(f,u,p,_,v,b,w,S,E=!1)=>{const x=f&&f.children,N=f?f.shapeFlag:0,C=u.children,{patchFlag:F,shapeFlag:L}=u;if(F>0){if(F&128){Ye(x,C,p,_,v,b,w,S,E);return}else if(F&256){He(x,C,p,_,v,b,w,S,E);return}}L&8?(N&16&&ye(x,v,b),C!==x&&a(p,C)):N&16?L&16?Ye(x,C,p,_,v,b,w,S,E):ye(x,v,b,!0):(N&8&&a(p,""),L&16&&we(C,p,_,v,b,w,S,E))},He=(f,u,p,_,v,b,w,S,E)=>{f=f||bt,u=u||bt;const x=f.length,N=u.length,C=Math.min(x,N);let F;for(F=0;F<C;F++){const L=u[F]=E?Xe(u[F]):Ne(u[F]);A(f[F],L,p,null,v,b,w,S,E)}x>N?ye(f,v,b,!0,!1,C):we(u,p,_,v,b,w,S,E,C)},Ye=(f,u,p,_,v,b,w,S,E)=>{let x=0;const N=u.length;let C=f.length-1,F=N-1;for(;x<=C&&x<=F;){const L=f[x],U=u[x]=E?Xe(u[x]):Ne(u[x]);if(Tt(L,U))A(L,U,p,null,v,b,w,S,E);else break;x++}for(;x<=C&&x<=F;){const L=f[C],U=u[F]=E?Xe(u[F]):Ne(u[F]);if(Tt(L,U))A(L,U,p,null,v,b,w,S,E);else break;C--,F--}if(x>C){if(x<=F){const L=F+1,U=L<N?u[L].el:_;for(;x<=F;)A(null,u[x]=E?Xe(u[x]):Ne(u[x]),p,U,v,b,w,S,E),x++}}else if(x>F)for(;x<=C;)ae(f[x],v,b,!0),x++;else{const L=x,U=x,Q=new Map;for(x=U;x<=F;x++){const ge=u[x]=E?Xe(u[x]):Ne(u[x]);ge.key!=null&&Q.set(ge.key,x)}let q,he=0;const de=F-U+1;let Oe=!1,Ae=0;const Ot=new Array(de);for(x=0;x<de;x++)Ot[x]=0;for(x=L;x<=C;x++){const ge=f[x];if(he>=de){ae(ge,v,b,!0);continue}let Te;if(ge.key!=null)Te=Q.get(ge.key);else for(q=U;q<=F;q++)if(Ot[q-U]===0&&Tt(ge,u[q])){Te=q;break}Te===void 0?ae(ge,v,b,!0):(Ot[Te-U]=x+1,Te>=Ae?Ae=Te:Oe=!0,A(ge,u[Te],p,null,v,b,w,S,E),he++)}const Rs=Oe?fl(Ot):bt;for(q=Rs.length-1,x=de-1;x>=0;x--){const ge=U+x,Te=u[ge],Cs=u[ge+1],Ps=ge+1<N?Cs.el||Cs.placeholder:_;Ot[x]===0?A(null,Te,p,Ps,v,b,w,S,E):Oe&&(q<0||x!==Rs[q]?Pe(Te,p,Ps,2):q--)}}},Pe=(f,u,p,_,v=null)=>{const{el:b,type:w,transition:S,children:E,shapeFlag:x}=f;if(x&6){Pe(f.component.subTree,u,p,_);return}if(x&128){f.suspense.move(u,p,_);return}if(x&64){w.move(f,u,p,I);return}if(w===Fe){s(b,u,p);for(let C=0;C<E.length;C++)Pe(E[C],u,p,_);s(f.anchor,u,p);return}if(w===Mn){j(f,u,p);return}if(_!==2&&x&1&&S)if(_===0)S.beforeEnter(b),s(b,u,p),me(()=>S.enter(b),v);else{const{leave:C,delayLeave:F,afterLeave:L}=S,U=()=>{f.ctx.isUnmounted?r(b):s(b,u,p)},Q=()=>{C(b,()=>{U(),L&&L()})};F?F(b,U,Q):Q()}else s(b,u,p)},ae=(f,u,p,_=!1,v=!1)=>{const{type:b,props:w,ref:S,children:E,dynamicChildren:x,shapeFlag:N,patchFlag:C,dirs:F,cacheIndex:L}=f;if(C===-2&&(v=!1),S!=null&&(We(),Lt(S,null,p,f,!0),ke()),L!=null&&(u.renderCache[L]=void 0),N&256){u.ctx.deactivate(f);return}const U=N&1&&F,Q=!Ht(f);let q;if(Q&&(q=w&&w.onVnodeBeforeUnmount)&&Ie(q,u,f),N&6)Xt(f.component,p,_);else{if(N&128){f.suspense.unmount(p,_);return}U&&it(f,null,u,"beforeUnmount"),N&64?f.type.remove(f,u,p,I,_):x&&!x.hasOnce&&(b!==Fe||C>0&&C&64)?ye(x,u,p,!1,!0):(b===Fe&&C&384||!v&&N&16)&&ye(E,u,p),_&&dt(f)}(Q&&(q=w&&w.onVnodeUnmounted)||U)&&me(()=>{q&&Ie(q,u,f),U&&it(f,null,u,"unmounted")},p)},dt=f=>{const{type:u,el:p,anchor:_,transition:v}=f;if(u===Fe){pt(p,_);return}if(u===Mn){T(f);return}const b=()=>{r(p),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(f.shapeFlag&1&&v&&!v.persisted){const{leave:w,delayLeave:S}=v,E=()=>w(p,b);S?S(f.el,b,E):E()}else b()},pt=(f,u)=>{let p;for(;f!==u;)p=g(f),r(f),f=p;r(u)},Xt=(f,u,p)=>{const{bum:_,scope:v,job:b,subTree:w,um:S,m:E,a:x,parent:N,slots:{__:C}}=f;Ls(E),Ls(x),_&&Rn(_),N&&H(C)&&C.forEach(F=>{N.renderCache[F]=void 0}),v.stop(),b&&(b.flags|=8,ae(w,f,u,p)),S&&me(S,u),me(()=>{f.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},ye=(f,u,p,_=!1,v=!1,b=0)=>{for(let w=b;w<f.length;w++)ae(f[w],u,p,_,v)},y=f=>{if(f.shapeFlag&6)return y(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const u=g(f.anchor||f.el),p=u&&u[Io];return p?g(p):u};let P=!1;const R=(f,u,p)=>{f==null?u._vnode&&ae(u._vnode,null,null,!0):A(u._vnode||null,f,u,null,null,null,p),u._vnode=f,P||(P=!0,Is(),Zr(),P=!1)},I={p:A,um:ae,m:Pe,r:dt,mt:Pt,mc:we,pc:V,pbc:Re,n:y,o:e};return{render:R,hydrate:void 0,createApp:Zo(R)}}function In({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ot({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function cl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function _i(e,t,n=!1){const s=e.children,r=t.children;if(H(s)&&H(r))for(let i=0;i<s.length;i++){const o=s[i];let c=r[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[i]=Xe(r[i]),c.el=o.el),!n&&c.patchFlag!==-2&&_i(o,c)),c.type===En&&(c.el=o.el),c.type===St&&!c.el&&(c.el=o.el)}}function fl(e){const t=e.slice(),n=[0];let s,r,i,o,c;const l=e.length;for(s=0;s<l;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)c=i+o>>1,e[n[c]]<d?i=c+1:o=c;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function yi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:yi(t)}function Ls(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ul=Symbol.for("v-scx"),al=()=>Ke(ul);function rn(e,t,n){return bi(e,t,n)}function bi(e,t,n=X){const{immediate:s,deep:r,flush:i,once:o}=n,c=oe({},n),l=t&&s||!t&&i!=="post";let d;if(Gt){if(i==="sync"){const m=al();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!l){const m=()=>{};return m.stop=je,m.resume=je,m.pause=je,m}}const a=ue;c.call=(m,O,A)=>Le(m,a,O,A);let h=!1;i==="post"?c.scheduler=m=>{me(m,a&&a.suspense)}:i!=="sync"&&(h=!0,c.scheduler=(m,O)=>{O?m():gs(m)}),c.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const g=Co(e,t,c);return Gt&&(d?d.push(g):l&&g()),g}function hl(e,t,n){const s=this.proxy,r=te(e)?e.includes(".")?vi(s,e):()=>s[e]:e.bind(s,s);let i;D(t)?i=t:(i=t.handler,n=t);const o=Jt(this),c=bi(r,i.bind(s),n);return o(),c}function vi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const dl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${nt(t)}Modifiers`]||e[`${at(t)}Modifiers`];function pl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||X;let r=n;const i=t.startsWith("update:"),o=i&&dl(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>te(a)?a.trim():a)),o.number&&(r=n.map(qi)));let c,l=s[c=wn(t)]||s[c=wn(nt(t))];!l&&i&&(l=s[c=wn(at(t))]),l&&Le(l,e,6,r);const d=s[c+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Le(d,e,6,r)}}function xi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},c=!1;if(!D(e)){const l=d=>{const a=xi(d,t,!0);a&&(c=!0,oe(o,a))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!c?(ee(e)&&s.set(e,null),null):(H(i)?i.forEach(l=>o[l]=null):oe(o,i),ee(e)&&s.set(e,o),o)}function xn(e,t){return!e||!pn(t)?!1:(t=t.slice(2).replace(/Once$/,""),k(e,t[0].toLowerCase()+t.slice(1))||k(e,at(t))||k(e,t))}function Hs(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:c,emit:l,render:d,renderCache:a,props:h,data:g,setupState:m,ctx:O,inheritAttrs:A}=e,B=un(e);let $,M;try{if(n.shapeFlag&4){const T=r||s,z=T;$=Ne(d.call(z,T,a,h,m,g,O)),M=c}else{const T=t;$=Ne(T.length>1?T(h,{attrs:c,slots:o,emit:l}):T(h,null)),M=t.props?c:gl(c)}}catch(T){Bt.length=0,bn(T,e,1),$=ve(St)}let j=$;if(M&&A!==!1){const T=Object.keys(M),{shapeFlag:z}=j;T.length&&z&7&&(i&&T.some(ns)&&(M=ml(M,i)),j=wt(j,M,!1,!0))}return n.dirs&&(j=wt(j,null,!1,!0),j.dirs=j.dirs?j.dirs.concat(n.dirs):n.dirs),n.transition&&ms(j,n.transition),$=j,un(B),$}const gl=e=>{let t;for(const n in e)(n==="class"||n==="style"||pn(n))&&((t||(t={}))[n]=e[n]);return t},ml=(e,t)=>{const n={};for(const s in e)(!ns(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function _l(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:c,patchFlag:l}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?Ds(s,o,d):!!o;if(l&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const g=a[h];if(o[g]!==s[g]&&!xn(d,g))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:s===o?!1:s?o?Ds(s,o,d):!0:!!o;return!1}function Ds(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!xn(n,i))return!0}return!1}function yl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ei=e=>e.__isSuspense;function bl(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Ao(e)}const Fe=Symbol.for("v-fgt"),En=Symbol.for("v-txt"),St=Symbol.for("v-cmt"),Mn=Symbol.for("v-stc"),Bt=[];let _e=null;function vl(e=!1){Bt.push(_e=e?null:[])}function xl(){Bt.pop(),_e=Bt[Bt.length-1]||null}let qt=1;function Bs(e,t=!1){qt+=e,e<0&&_e&&t&&(_e.hasOnce=!0)}function El(e){return e.dynamicChildren=qt>0?_e||bt:null,xl(),qt>0&&_e&&_e.push(e),e}function Sl(e,t,n,s,r,i){return El(yt(e,t,n,s,r,i,!0))}function hn(e){return e?e.__v_isVNode===!0:!1}function Tt(e,t){return e.type===t.type&&e.key===t.key}const Si=({key:e})=>e??null,on=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?te(e)||ie(e)||D(e)?{i:$e,r:e,k:t,f:!!n}:e:null);function yt(e,t=null,n=null,s=0,r=null,i=e===Fe?0:1,o=!1,c=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Si(t),ref:t&&on(t),scopeId:ti,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:$e};return c?(vs(l,n),i&128&&e.normalize(l)):n&&(l.shapeFlag|=te(n)?8:16),qt>0&&!o&&_e&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&_e.push(l),l}const ve=wl;function wl(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===ko)&&(e=St),hn(e)){const c=wt(e,t,!0);return n&&vs(c,n),qt>0&&!i&&_e&&(c.shapeFlag&6?_e[_e.indexOf(e)]=c:_e.push(c)),c.patchFlag=-2,c}if($l(e)&&(e=e.__vccOpts),t){t=Rl(t);let{class:c,style:l}=t;c&&!te(c)&&(t.class=os(c)),ee(l)&&(ps(l)&&!H(l)&&(l=oe({},l)),t.style=is(l))}const o=te(e)?1:Ei(e)?128:Mo(e)?64:ee(e)?4:D(e)?2:0;return yt(e,t,n,s,r,o,i,!0)}function Rl(e){return e?ps(e)||ai(e)?oe({},e):e:null}function wt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:c,transition:l}=e,d=t?Cl(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&Si(d),ref:t&&t.ref?n&&i?H(i)?i.concat(on(t)):[i,on(t)]:on(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&wt(e.ssContent),ssFallback:e.ssFallback&&wt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&ms(a,l.clone(a)),a}function Yn(e=" ",t=0){return ve(En,null,e,t)}function Ne(e){return e==null||typeof e=="boolean"?ve(St):H(e)?ve(Fe,null,e.slice()):hn(e)?Xe(e):ve(En,null,String(e))}function Xe(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:wt(e)}function vs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),vs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!ai(t)?t._ctx=$e:r===3&&$e&&($e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else D(t)?(t={default:t,_ctx:$e},n=32):(t=String(t),s&64?(n=16,t=[Yn(t)]):n=8);e.children=t,e.shapeFlag|=n}function Cl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=os([t.class,s.class]));else if(r==="style")t.style=is([t.style,s.style]);else if(pn(r)){const i=t[r],o=s[r];o&&i!==o&&!(H(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Ie(e,t,n,s=null){Le(e,t,7,[n,s])}const Pl=ci();let Ol=0;function Al(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Pl,i={uid:Ol++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ar(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:di(s,r),emitsOptions:xi(s,r),emit:null,emitted:null,propsDefaults:X,inheritAttrs:s.inheritAttrs,ctx:X,data:X,props:X,attrs:X,slots:X,refs:X,setupState:X,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=pl.bind(null,i),e.ce&&e.ce(i),i}let ue=null;const Tl=()=>ue||$e;let dn,Qn;{const e=_n(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};dn=t("__VUE_INSTANCE_SETTERS__",n=>ue=n),Qn=t("__VUE_SSR_SETTERS__",n=>Gt=n)}const Jt=e=>{const t=ue;return dn(e),e.scope.on(),()=>{e.scope.off(),dn(t)}},Us=()=>{ue&&ue.scope.off(),dn(null)};function wi(e){return e.vnode.shapeFlag&4}let Gt=!1;function Il(e,t=!1,n=!1){t&&Qn(t);const{props:s,children:r}=e.vnode,i=wi(e);el(e,s,i,t),rl(e,r,n||t);const o=i?Ml(e,t):void 0;return t&&Qn(!1),o}function Ml(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,qo);const{setup:s}=n;if(s){We();const r=e.setupContext=s.length>1?Nl(e):null,i=Jt(e),o=Qt(s,e,0,[e.props,r]),c=xr(o);if(ke(),i(),(c||e.sp)&&!Ht(e)&&ni(e),c){if(o.then(Us,Us),t)return o.then(l=>{Vs(e,l)}).catch(l=>{bn(l,e,0)});e.asyncDep=o}else Vs(e,o)}else Ri(e)}function Vs(e,t,n){D(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ee(t)&&(e.setupState=Yr(t)),Ri(e)}function Ri(e,t,n){const s=e.type;e.render||(e.render=s.render||je);{const r=Jt(e);We();try{Go(e)}finally{ke(),r()}}}const Fl={get(e,t){return re(e,"get",""),e[t]}};function Nl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Fl),slots:e.slots,emit:e.emit,expose:t}}function xs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Yr(qr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Dt)return Dt[n](e)},has(t,n){return n in t||n in Dt}})):e.proxy}function $l(e){return D(e)&&"__vccOpts"in e}const be=(e,t)=>wo(e,t,Gt);function Ci(e,t,n){const s=arguments.length;return s===2?ee(t)&&!H(t)?hn(t)?ve(e,null,[t]):ve(e,t):ve(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&hn(n)&&(n=[n]),ve(e,t,n))}const jl="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Jn;const Ks=typeof window<"u"&&window.trustedTypes;if(Ks)try{Jn=Ks.createPolicy("vue",{createHTML:e=>e})}catch{}const Pi=Jn?e=>Jn.createHTML(e):e=>e,Ll="http://www.w3.org/2000/svg",Hl="http://www.w3.org/1998/Math/MathML",Ue=typeof document<"u"?document:null,Ws=Ue&&Ue.createElement("template"),Dl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ue.createElementNS(Ll,e):t==="mathml"?Ue.createElementNS(Hl,e):n?Ue.createElement(e,{is:n}):Ue.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ue.createTextNode(e),createComment:e=>Ue.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ue.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Ws.innerHTML=Pi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const c=Ws.content;if(s==="svg"||s==="mathml"){const l=c.firstChild;for(;l.firstChild;)c.appendChild(l.firstChild);c.removeChild(l)}t.insertBefore(c,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Bl=Symbol("_vtc");function Ul(e,t,n){const s=e[Bl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ks=Symbol("_vod"),Vl=Symbol("_vsh"),Kl=Symbol(""),Wl=/(^|;)\s*display\s*:/;function kl(e,t,n){const s=e.style,r=te(n);let i=!1;if(n&&!r){if(t)if(te(t))for(const o of t.split(";")){const c=o.slice(0,o.indexOf(":")).trim();n[c]==null&&ln(s,c,"")}else for(const o in t)n[o]==null&&ln(s,o,"");for(const o in n)o==="display"&&(i=!0),ln(s,o,n[o])}else if(r){if(t!==n){const o=s[Kl];o&&(n+=";"+o),s.cssText=n,i=Wl.test(n)}}else t&&e.removeAttribute("style");ks in e&&(e[ks]=i?s.display:"",e[Vl]&&(s.display="none"))}const qs=/\s*!important$/;function ln(e,t,n){if(H(n))n.forEach(s=>ln(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=ql(e,t);qs.test(n)?e.setProperty(at(s),n.replace(qs,""),"important"):e[s]=n}}const Gs=["Webkit","Moz","ms"],Fn={};function ql(e,t){const n=Fn[t];if(n)return n;let s=nt(t);if(s!=="filter"&&s in e)return Fn[t]=s;s=wr(s);for(let r=0;r<Gs.length;r++){const i=Gs[r]+s;if(i in e)return Fn[t]=i}return t}const zs="http://www.w3.org/1999/xlink";function Ys(e,t,n,s,r,i=Xi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(zs,t.slice(6,t.length)):e.setAttributeNS(zs,t,n):n==null||i&&!Rr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":st(n)?String(n):n)}function Qs(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Pi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(c!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=Rr(n):n==null&&c==="string"?(n="",o=!0):c==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function Gl(e,t,n,s){e.addEventListener(t,n,s)}function zl(e,t,n,s){e.removeEventListener(t,n,s)}const Js=Symbol("_vei");function Yl(e,t,n,s,r=null){const i=e[Js]||(e[Js]={}),o=i[t];if(s&&o)o.value=s;else{const[c,l]=Ql(t);if(s){const d=i[t]=Zl(s,r);Gl(e,c,d,l)}else o&&(zl(e,c,o,l),i[t]=void 0)}}const Xs=/(?:Once|Passive|Capture)$/;function Ql(e){let t;if(Xs.test(e)){t={};let s;for(;s=e.match(Xs);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):at(e.slice(2)),t]}let Nn=0;const Jl=Promise.resolve(),Xl=()=>Nn||(Jl.then(()=>Nn=0),Nn=Date.now());function Zl(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Le(ec(s,n.value),t,5,[s])};return n.value=e,n.attached=Xl(),n}function ec(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Zs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,tc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Ul(e,s,o):t==="style"?kl(e,n,s):pn(t)?ns(t)||Yl(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):nc(e,t,s,o))?(Qs(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ys(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!te(s))?Qs(e,nt(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Ys(e,t,s,o))};function nc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Zs(t)&&D(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Zs(t)&&te(n)?!1:t in e}const sc=oe({patchProp:tc},Dl);let er;function rc(){return er||(er=ol(sc))}const ic=(...e)=>{const t=rc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=lc(s);if(!r)return;const i=t._component;!D(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,oc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function oc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function lc(e){return te(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const cc=Symbol();var tr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(tr||(tr={}));function fc(){const e=Zi(!0),t=e.run(()=>Gr({}));let n=[],s=[];const r=qr({install(i){r._a=i,i.provide(cc,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return this._a?n.push(i):s.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const $n=e=>typeof e=="string",nr=e=>typeof e=="number",uc=e=>typeof e=="function",ac=Array.isArray,hc=function(e){return typeof e=="object"&&e!==null},nn="hello",dc=_s({__name:"App",setup(e){return $n(nn)&&console.log("String length:",nn.length),nr(nn)&&console.log("Number value:",nn.toFixed(2)),console.log("Type checks:",{aisString:$n("test"),isNumber:nr(123),isFunction:uc(()=>{}),isArray:ac([1,2,3]),isObject:hc({})}),(t,n)=>(vl(),Sl(Fe,null,[yt("div",null,Pr(ft($n)("2222")),1),n[0]||(n[0]=yt("h1",null,"You did it!",-1)),n[1]||(n[1]=yt("p",null,[Yn(" Visit "),yt("a",{href:"https://vuejs.org/",target:"_blank",rel:"noopener"},"vuejs.org"),Yn(" to read the documentation ")],-1))],64))}});/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const _t=typeof document<"u";function Oi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function pc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Oi(e.default)}const K=Object.assign;function jn(e,t){const n={};for(const s in t){const r=t[s];n[s]=Se(r)?r.map(e):e(r)}return n}const Ut=()=>{},Se=Array.isArray,Ai=/#/g,gc=/&/g,mc=/\//g,_c=/=/g,yc=/\?/g,Ti=/\+/g,bc=/%5B/g,vc=/%5D/g,Ii=/%5E/g,xc=/%60/g,Mi=/%7B/g,Ec=/%7C/g,Fi=/%7D/g,Sc=/%20/g;function Es(e){return encodeURI(""+e).replace(Ec,"|").replace(bc,"[").replace(vc,"]")}function wc(e){return Es(e).replace(Mi,"{").replace(Fi,"}").replace(Ii,"^")}function Xn(e){return Es(e).replace(Ti,"%2B").replace(Sc,"+").replace(Ai,"%23").replace(gc,"%26").replace(xc,"`").replace(Mi,"{").replace(Fi,"}").replace(Ii,"^")}function Rc(e){return Xn(e).replace(_c,"%3D")}function Cc(e){return Es(e).replace(Ai,"%23").replace(yc,"%3F")}function Pc(e){return e==null?"":Cc(e).replace(mc,"%2F")}function zt(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Oc=/\/$/,Ac=e=>e.replace(Oc,"");function Ln(e,t,n="/"){let s,r={},i="",o="";const c=t.indexOf("#");let l=t.indexOf("?");return c<l&&c>=0&&(l=-1),l>-1&&(s=t.slice(0,l),i=t.slice(l+1,c>-1?c:t.length),r=e(i)),c>-1&&(s=s||t.slice(0,c),o=t.slice(c,t.length)),s=Fc(s??t,n),{fullPath:s+(i&&"?")+i+o,path:s,query:r,hash:zt(o)}}function Tc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function sr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ic(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Rt(t.matched[s],n.matched[r])&&Ni(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Rt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ni(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Mc(e[n],t[n]))return!1;return!0}function Mc(e,t){return Se(e)?rr(e,t):Se(t)?rr(t,e):e===t}function rr(e,t){return Se(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Fc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let i=n.length-1,o,c;for(o=0;o<s.length;o++)if(c=s[o],c!==".")if(c==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const Qe={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Yt;(function(e){e.pop="pop",e.push="push"})(Yt||(Yt={}));var Vt;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Vt||(Vt={}));function Nc(e){if(!e)if(_t){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Ac(e)}const $c=/^[^#]+#/;function jc(e,t){return e.replace($c,"#")+t}function Lc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Sn=()=>({left:window.scrollX,top:window.scrollY});function Hc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Lc(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function ir(e,t){return(history.state?history.state.position-t:-1)+e}const Zn=new Map;function Dc(e,t){Zn.set(e,t)}function Bc(e){const t=Zn.get(e);return Zn.delete(e),t}let Uc=()=>location.protocol+"//"+location.host;function $i(e,t){const{pathname:n,search:s,hash:r}=t,i=e.indexOf("#");if(i>-1){let c=r.includes(e.slice(i))?e.slice(i).length:1,l=r.slice(c);return l[0]!=="/"&&(l="/"+l),sr(l,"")}return sr(n,e)+s+r}function Vc(e,t,n,s){let r=[],i=[],o=null;const c=({state:g})=>{const m=$i(e,location),O=n.value,A=t.value;let B=0;if(g){if(n.value=m,t.value=g,o&&o===O){o=null;return}B=A?g.position-A.position:0}else s(m);r.forEach($=>{$(n.value,O,{delta:B,type:Yt.pop,direction:B?B>0?Vt.forward:Vt.back:Vt.unknown})})};function l(){o=n.value}function d(g){r.push(g);const m=()=>{const O=r.indexOf(g);O>-1&&r.splice(O,1)};return i.push(m),m}function a(){const{history:g}=window;g.state&&g.replaceState(K({},g.state,{scroll:Sn()}),"")}function h(){for(const g of i)g();i=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:l,listen:d,destroy:h}}function or(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Sn():null}}function Kc(e){const{history:t,location:n}=window,s={value:$i(e,n)},r={value:t.state};r.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,d,a){const h=e.indexOf("#"),g=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+l:Uc()+e+l;try{t[a?"replaceState":"pushState"](d,"",g),r.value=d}catch(m){console.error(m),n[a?"replace":"assign"](g)}}function o(l,d){const a=K({},t.state,or(r.value.back,l,r.value.forward,!0),d,{position:r.value.position});i(l,a,!0),s.value=l}function c(l,d){const a=K({},r.value,t.state,{forward:l,scroll:Sn()});i(a.current,a,!0);const h=K({},or(s.value,l,null),{position:a.position+1},d);i(l,h,!1),s.value=l}return{location:s,state:r,push:c,replace:o}}function Wc(e){e=Nc(e);const t=Kc(e),n=Vc(e,t.state,t.location,t.replace);function s(i,o=!0){o||n.pauseListeners(),history.go(i)}const r=K({location:"",base:e,go:s,createHref:jc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function kc(e){return typeof e=="string"||e&&typeof e=="object"}function ji(e){return typeof e=="string"||typeof e=="symbol"}const Li=Symbol("");var lr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(lr||(lr={}));function Ct(e,t){return K(new Error,{type:e,[Li]:!0},t)}function Be(e,t){return e instanceof Error&&Li in e&&(t==null||!!(e.type&t))}const cr="[^/]+?",qc={sensitive:!1,strict:!1,start:!0,end:!0},Gc=/[.+*?^${}()[\]/\\]/g;function zc(e,t){const n=K({},qc,t),s=[];let r=n.start?"^":"";const i=[];for(const d of e){const a=d.length?[]:[90];n.strict&&!d.length&&(r+="/");for(let h=0;h<d.length;h++){const g=d[h];let m=40+(n.sensitive?.25:0);if(g.type===0)h||(r+="/"),r+=g.value.replace(Gc,"\\$&"),m+=40;else if(g.type===1){const{value:O,repeatable:A,optional:B,regexp:$}=g;i.push({name:O,repeatable:A,optional:B});const M=$||cr;if(M!==cr){m+=10;try{new RegExp(`(${M})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${O}" (${M}): `+T.message)}}let j=A?`((?:${M})(?:/(?:${M}))*)`:`(${M})`;h||(j=B&&d.length<2?`(?:/${j})`:"/"+j),B&&(j+="?"),r+=j,m+=20,B&&(m+=-8),A&&(m+=-20),M===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const o=new RegExp(r,n.sensitive?"":"i");function c(d){const a=d.match(o),h={};if(!a)return null;for(let g=1;g<a.length;g++){const m=a[g]||"",O=i[g-1];h[O.name]=m&&O.repeatable?m.split("/"):m}return h}function l(d){let a="",h=!1;for(const g of e){(!h||!a.endsWith("/"))&&(a+="/"),h=!1;for(const m of g)if(m.type===0)a+=m.value;else if(m.type===1){const{value:O,repeatable:A,optional:B}=m,$=O in d?d[O]:"";if(Se($)&&!A)throw new Error(`Provided param "${O}" is an array but it is not repeatable (* or + modifiers)`);const M=Se($)?$.join("/"):$;if(!M)if(B)g.length<2&&(a.endsWith("/")?a=a.slice(0,-1):h=!0);else throw new Error(`Missing required param "${O}"`);a+=M}}return a||"/"}return{re:o,score:s,keys:i,parse:c,stringify:l}}function Yc(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Hi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const i=Yc(s[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-s.length)===1){if(fr(s))return 1;if(fr(r))return-1}return r.length-s.length}function fr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Qc={type:0,value:""},Jc=/[a-zA-Z0-9_]/;function Xc(e){if(!e)return[[]];if(e==="/")return[[Qc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const r=[];let i;function o(){i&&r.push(i),i=[]}let c=0,l,d="",a="";function h(){d&&(n===0?i.push({type:0,value:d}):n===1||n===2||n===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:d,regexp:a,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),d="")}function g(){d+=l}for(;c<e.length;){if(l=e[c++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(d&&h(),o()):l===":"?(h(),n=1):g();break;case 4:g(),n=s;break;case 1:l==="("?n=2:Jc.test(l)?g():(h(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--);break;case 2:l===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+l:n=3:a+=l;break;case 3:h(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&c--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),h(),o(),r}function Zc(e,t,n){const s=zc(Xc(e.path),n),r=K(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ef(e,t){const n=[],s=new Map;t=dr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function i(h,g,m){const O=!m,A=ar(h);A.aliasOf=m&&m.record;const B=dr(t,h),$=[A];if("alias"in h){const T=typeof h.alias=="string"?[h.alias]:h.alias;for(const z of T)$.push(ar(K({},A,{components:m?m.record.components:A.components,path:z,aliasOf:m?m.record:A})))}let M,j;for(const T of $){const{path:z}=T;if(g&&z[0]!=="/"){const se=g.record.path,Z=se[se.length-1]==="/"?"":"/";T.path=g.record.path+(z&&Z+z)}if(M=Zc(T,g,B),m?m.alias.push(M):(j=j||M,j!==M&&j.alias.push(M),O&&h.name&&!hr(M)&&o(h.name)),Di(M)&&l(M),A.children){const se=A.children;for(let Z=0;Z<se.length;Z++)i(se[Z],M,m&&m.children[Z])}m=m||M}return j?()=>{o(j)}:Ut}function o(h){if(ji(h)){const g=s.get(h);g&&(s.delete(h),n.splice(n.indexOf(g),1),g.children.forEach(o),g.alias.forEach(o))}else{const g=n.indexOf(h);g>-1&&(n.splice(g,1),h.record.name&&s.delete(h.record.name),h.children.forEach(o),h.alias.forEach(o))}}function c(){return n}function l(h){const g=sf(h,n);n.splice(g,0,h),h.record.name&&!hr(h)&&s.set(h.record.name,h)}function d(h,g){let m,O={},A,B;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw Ct(1,{location:h});B=m.record.name,O=K(ur(g.params,m.keys.filter(j=>!j.optional).concat(m.parent?m.parent.keys.filter(j=>j.optional):[]).map(j=>j.name)),h.params&&ur(h.params,m.keys.map(j=>j.name))),A=m.stringify(O)}else if(h.path!=null)A=h.path,m=n.find(j=>j.re.test(A)),m&&(O=m.parse(A),B=m.record.name);else{if(m=g.name?s.get(g.name):n.find(j=>j.re.test(g.path)),!m)throw Ct(1,{location:h,currentLocation:g});B=m.record.name,O=K({},g.params,h.params),A=m.stringify(O)}const $=[];let M=m;for(;M;)$.unshift(M.record),M=M.parent;return{name:B,path:A,params:O,matched:$,meta:nf($)}}e.forEach(h=>i(h));function a(){n.length=0,s.clear()}return{addRoute:i,resolve:d,removeRoute:o,clearRoutes:a,getRoutes:c,getRecordMatcher:r}}function ur(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function ar(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:tf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function tf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function hr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function nf(e){return e.reduce((t,n)=>K(t,n.meta),{})}function dr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function sf(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;Hi(e,t[i])<0?s=i:n=i+1}const r=rf(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function rf(e){let t=e;for(;t=t.parent;)if(Di(t)&&Hi(e,t)===0)return t}function Di({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function of(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const i=s[r].replace(Ti," "),o=i.indexOf("="),c=zt(o<0?i:i.slice(0,o)),l=o<0?null:zt(i.slice(o+1));if(c in t){let d=t[c];Se(d)||(d=t[c]=[d]),d.push(l)}else t[c]=l}return t}function pr(e){let t="";for(let n in e){const s=e[n];if(n=Rc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Se(s)?s.map(i=>i&&Xn(i)):[s&&Xn(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function lf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Se(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const cf=Symbol(""),gr=Symbol(""),Ss=Symbol(""),Bi=Symbol(""),es=Symbol("");function It(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ze(e,t,n,s,r,i=o=>o()){const o=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((c,l)=>{const d=g=>{g===!1?l(Ct(4,{from:n,to:t})):g instanceof Error?l(g):kc(g)?l(Ct(2,{from:t,to:g})):(o&&s.enterCallbacks[r]===o&&typeof g=="function"&&o.push(g),c())},a=i(()=>e.call(s&&s.instances[r],t,n,d));let h=Promise.resolve(a);e.length<3&&(h=h.then(d)),h.catch(g=>l(g))})}function Hn(e,t,n,s,r=i=>i()){const i=[];for(const o of e)for(const c in o.components){let l=o.components[c];if(!(t!=="beforeRouteEnter"&&!o.instances[c]))if(Oi(l)){const a=(l.__vccOpts||l)[t];a&&i.push(Ze(a,n,s,o,c,r))}else{let d=l();i.push(()=>d.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${c}" at "${o.path}"`);const h=pc(a)?a.default:a;o.mods[c]=a,o.components[c]=h;const m=(h.__vccOpts||h)[t];return m&&Ze(m,n,s,o,c,r)()}))}}return i}function mr(e){const t=Ke(Ss),n=Ke(Bi),s=be(()=>{const l=ft(e.to);return t.resolve(l)}),r=be(()=>{const{matched:l}=s.value,{length:d}=l,a=l[d-1],h=n.matched;if(!a||!h.length)return-1;const g=h.findIndex(Rt.bind(null,a));if(g>-1)return g;const m=_r(l[d-2]);return d>1&&_r(a)===m&&h[h.length-1].path!==m?h.findIndex(Rt.bind(null,l[d-2])):g}),i=be(()=>r.value>-1&&df(n.params,s.value.params)),o=be(()=>r.value>-1&&r.value===n.matched.length-1&&Ni(n.params,s.value.params));function c(l={}){if(hf(l)){const d=t[ft(e.replace)?"replace":"push"](ft(e.to)).catch(Ut);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:be(()=>s.value.href),isActive:i,isExactActive:o,navigate:c}}function ff(e){return e.length===1?e[0]:e}const uf=_s({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:mr,setup(e,{slots:t}){const n=yn(mr(e)),{options:s}=Ke(Ss),r=be(()=>({[yr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[yr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&ff(t.default(n));return e.custom?i:Ci("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),af=uf;function hf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function df(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Se(r)||r.length!==s.length||s.some((i,o)=>i!==r[o]))return!1}return!0}function _r(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const yr=(e,t,n)=>e??t??n,pf=_s({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ke(es),r=be(()=>e.route||s.value),i=Ke(gr,0),o=be(()=>{let d=ft(i);const{matched:a}=r.value;let h;for(;(h=a[d])&&!h.components;)d++;return d}),c=be(()=>r.value.matched[o.value]);sn(gr,be(()=>o.value+1)),sn(cf,c),sn(es,r);const l=Gr();return rn(()=>[l.value,c.value,e.name],([d,a,h],[g,m,O])=>{a&&(a.instances[h]=d,m&&m!==a&&d&&d===g&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),d&&a&&(!m||!Rt(a,m)||!g)&&(a.enterCallbacks[h]||[]).forEach(A=>A(d))},{flush:"post"}),()=>{const d=r.value,a=e.name,h=c.value,g=h&&h.components[a];if(!g)return br(n.default,{Component:g,route:d});const m=h.props[a],O=m?m===!0?d.params:typeof m=="function"?m(d):m:null,B=Ci(g,K({},O,t,{onVnodeUnmounted:$=>{$.component.isUnmounted&&(h.instances[a]=null)},ref:l}));return br(n.default,{Component:B,route:d})||B}}});function br(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const gf=pf;function mf(e){const t=ef(e.routes,e),n=e.parseQuery||of,s=e.stringifyQuery||pr,r=e.history,i=It(),o=It(),c=It(),l=vo(Qe);let d=Qe;_t&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=jn.bind(null,y=>""+y),h=jn.bind(null,Pc),g=jn.bind(null,zt);function m(y,P){let R,I;return ji(y)?(R=t.getRecordMatcher(y),I=P):I=y,t.addRoute(I,R)}function O(y){const P=t.getRecordMatcher(y);P&&t.removeRoute(P)}function A(){return t.getRoutes().map(y=>y.record)}function B(y){return!!t.getRecordMatcher(y)}function $(y,P){if(P=K({},P||l.value),typeof y=="string"){const p=Ln(n,y,P.path),_=t.resolve({path:p.path},P),v=r.createHref(p.fullPath);return K(p,_,{params:g(_.params),hash:zt(p.hash),redirectedFrom:void 0,href:v})}let R;if(y.path!=null)R=K({},y,{path:Ln(n,y.path,P.path).path});else{const p=K({},y.params);for(const _ in p)p[_]==null&&delete p[_];R=K({},y,{params:h(p)}),P.params=h(P.params)}const I=t.resolve(R,P),Y=y.hash||"";I.params=a(g(I.params));const f=Tc(s,K({},y,{hash:wc(Y),path:I.path})),u=r.createHref(f);return K({fullPath:f,hash:Y,query:s===pr?lf(y.query):y.query||{}},I,{redirectedFrom:void 0,href:u})}function M(y){return typeof y=="string"?Ln(n,y,l.value.path):K({},y)}function j(y,P){if(d!==y)return Ct(8,{from:P,to:y})}function T(y){return Z(y)}function z(y){return T(K(M(y),{replace:!0}))}function se(y){const P=y.matched[y.matched.length-1];if(P&&P.redirect){const{redirect:R}=P;let I=typeof R=="function"?R(y):R;return typeof I=="string"&&(I=I.includes("?")||I.includes("#")?I=M(I):{path:I},I.params={}),K({query:y.query,hash:y.hash,params:I.path!=null?{}:y.params},I)}}function Z(y,P){const R=d=$(y),I=l.value,Y=y.state,f=y.force,u=y.replace===!0,p=se(R);if(p)return Z(K(M(p),{state:typeof p=="object"?K({},Y,p.state):Y,force:f,replace:u}),P||R);const _=R;_.redirectedFrom=P;let v;return!f&&Ic(s,I,R)&&(v=Ct(16,{to:_,from:I}),Pe(I,I,!0,!1)),(v?Promise.resolve(v):Re(_,I)).catch(b=>Be(b)?Be(b,2)?b:Ye(b):V(b,_,I)).then(b=>{if(b){if(Be(b,2))return Z(K({replace:u},M(b.to),{state:typeof b.to=="object"?K({},Y,b.to.state):Y,force:f}),P||_)}else b=rt(_,I,!0,u,Y);return ze(_,I,b),b})}function we(y,P){const R=j(y,P);return R?Promise.reject(R):Promise.resolve()}function Ge(y){const P=pt.values().next().value;return P&&typeof P.runWithContext=="function"?P.runWithContext(y):y()}function Re(y,P){let R;const[I,Y,f]=_f(y,P);R=Hn(I.reverse(),"beforeRouteLeave",y,P);for(const p of I)p.leaveGuards.forEach(_=>{R.push(Ze(_,y,P))});const u=we.bind(null,y,P);return R.push(u),ye(R).then(()=>{R=[];for(const p of i.list())R.push(Ze(p,y,P));return R.push(u),ye(R)}).then(()=>{R=Hn(Y,"beforeRouteUpdate",y,P);for(const p of Y)p.updateGuards.forEach(_=>{R.push(Ze(_,y,P))});return R.push(u),ye(R)}).then(()=>{R=[];for(const p of f)if(p.beforeEnter)if(Se(p.beforeEnter))for(const _ of p.beforeEnter)R.push(Ze(_,y,P));else R.push(Ze(p.beforeEnter,y,P));return R.push(u),ye(R)}).then(()=>(y.matched.forEach(p=>p.enterCallbacks={}),R=Hn(f,"beforeRouteEnter",y,P,Ge),R.push(u),ye(R))).then(()=>{R=[];for(const p of o.list())R.push(Ze(p,y,P));return R.push(u),ye(R)}).catch(p=>Be(p,8)?p:Promise.reject(p))}function ze(y,P,R){c.list().forEach(I=>Ge(()=>I(y,P,R)))}function rt(y,P,R,I,Y){const f=j(y,P);if(f)return f;const u=P===Qe,p=_t?history.state:{};R&&(I||u?r.replace(y.fullPath,K({scroll:u&&p&&p.scroll},Y)):r.push(y.fullPath,Y)),l.value=y,Pe(y,P,R,u),Ye()}let Ce;function Pt(){Ce||(Ce=r.listen((y,P,R)=>{if(!Xt.listening)return;const I=$(y),Y=se(I);if(Y){Z(K(Y,{replace:!0,force:!0}),I).catch(Ut);return}d=I;const f=l.value;_t&&Dc(ir(f.fullPath,R.delta),Sn()),Re(I,f).catch(u=>Be(u,12)?u:Be(u,2)?(Z(K(M(u.to),{force:!0}),I).then(p=>{Be(p,20)&&!R.delta&&R.type===Yt.pop&&r.go(-1,!1)}).catch(Ut),Promise.reject()):(R.delta&&r.go(-R.delta,!1),V(u,I,f))).then(u=>{u=u||rt(I,f,!1),u&&(R.delta&&!Be(u,8)?r.go(-R.delta,!1):R.type===Yt.pop&&Be(u,20)&&r.go(-1,!1)),ze(I,f,u)}).catch(Ut)}))}let ht=It(),ne=It(),G;function V(y,P,R){Ye(y);const I=ne.list();return I.length?I.forEach(Y=>Y(y,P,R)):console.error(y),Promise.reject(y)}function He(){return G&&l.value!==Qe?Promise.resolve():new Promise((y,P)=>{ht.add([y,P])})}function Ye(y){return G||(G=!y,Pt(),ht.list().forEach(([P,R])=>y?R(y):P()),ht.reset()),y}function Pe(y,P,R,I){const{scrollBehavior:Y}=e;if(!_t||!Y)return Promise.resolve();const f=!R&&Bc(ir(y.fullPath,0))||(I||!R)&&history.state&&history.state.scroll||null;return Jr().then(()=>Y(y,P,f)).then(u=>u&&Hc(u)).catch(u=>V(u,y,P))}const ae=y=>r.go(y);let dt;const pt=new Set,Xt={currentRoute:l,listening:!0,addRoute:m,removeRoute:O,clearRoutes:t.clearRoutes,hasRoute:B,getRoutes:A,resolve:$,options:e,push:T,replace:z,go:ae,back:()=>ae(-1),forward:()=>ae(1),beforeEach:i.add,beforeResolve:o.add,afterEach:c.add,onError:ne.add,isReady:He,install(y){const P=this;y.component("RouterLink",af),y.component("RouterView",gf),y.config.globalProperties.$router=P,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>ft(l)}),_t&&!dt&&l.value===Qe&&(dt=!0,T(r.location).catch(Y=>{}));const R={};for(const Y in Qe)Object.defineProperty(R,Y,{get:()=>l.value[Y],enumerable:!0});y.provide(Ss,P),y.provide(Bi,Wr(R)),y.provide(es,l);const I=y.unmount;pt.add(y),y.unmount=function(){pt.delete(y),pt.size<1&&(d=Qe,Ce&&Ce(),Ce=null,l.value=Qe,dt=!1,G=!1),I()}}};function ye(y){return y.reduce((P,R)=>P.then(()=>Ge(R)),Promise.resolve())}return Xt}function _f(e,t){const n=[],s=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const c=t.matched[o];c&&(e.matched.find(d=>Rt(d,c))?s.push(c):n.push(c));const l=e.matched[o];l&&(t.matched.find(d=>Rt(d,l))||r.push(l))}return[n,s,r]}const yf=mf({history:Wc("/"),routes:[]}),ws=ic(dc);ws.use(fc());ws.use(yf);ws.mount("#app");
