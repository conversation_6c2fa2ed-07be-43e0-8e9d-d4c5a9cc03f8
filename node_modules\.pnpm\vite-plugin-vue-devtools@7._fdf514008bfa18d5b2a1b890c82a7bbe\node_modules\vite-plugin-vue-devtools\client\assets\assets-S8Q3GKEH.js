import{d as T,aq as re,ay as Z,i as _,ad as le,c as p,a as P,o as a,b as r,F as z,D as F,n as M,u as s,t as x,l as C,ai as ae,e as S,q as A,x as X,r as L,M as Q,w as E,aa as W,az as ue,av as se,k as ne,Y as de,aA as Y,al as ce,C as D,aB as pe,f as ee,s as q,a3 as oe,ax as H,H as G,a9 as te,aj as he,aC as fe,aD as me,aw as ve,a1 as ye,G as ge,I as be,at as we,v as _e}from"./index-Dwf2ZVS8.js";import{_ as $e}from"./SectionBlock-B1R4b7tt.js";import{_ as xe}from"./IconTitle.vue_vue_type_script_setup_true_lang-DcR_j93Q.js";const ke={key:0,relative:"","code-block":""},Be={flex:"~ wrap","w-full":""},Ce=["onClick"],Ae={flex:"~ gap-2",px3:"",pb3:""},Pe=T({__name:"CodeSnippets",props:{codeSnippets:{},eventType:{}},setup(f){const n=f,e=re(n.codeSnippets[0]),{copy:t}=Z(),i=_(()=>e.value?.lang||"text");return le(()=>{n.codeSnippets.includes(e.value)||(e.value=n.codeSnippets[0])}),(o,l)=>o.codeSnippets.length?(a(),p("div",ke,[r("div",Be,[(a(!0),p(z,null,F(o.codeSnippets,(d,m)=>(a(),p("button",{key:m,px4:"",py2:"",border:"r base",hover:"bg-active",class:M(d===s(e)?"":"border-b"),onClick:v=>e.value=d},[r("div",{class:M(d===s(e)?"":"op30"),"font-mono":""},x(d.name),3)],10,Ce))),128)),l[1]||(l[1]=r("div",{border:"b base","flex-auto":""},null,-1))]),s(e)?(a(),p(z,{key:0},[C(s(ae),{code:s(e).code,lang:s(i),lines:!1,"w-full":"","of-auto":"",p3:""},null,8,["code","lang"]),r("div",Ae,[C(s(Q),{onClick:l[0]||(l[0]=d=>s(t)(s(e).code,{silent:!1,type:o.eventType||`code-snippet-${s(e).name}`}))},{icon:A(()=>[L(o.$slots,"i-carbon-copy")]),default:A(()=>[l[2]||(l[2]=X(" Copy "))]),_:3}),s(e)?.docs?(a(),S(s(Q),{key:0,to:s(e).docs,target:"_blank"},{icon:A(()=>[L(o.$slots,"i-carbon-catalog")]),default:A(()=>[l[3]||(l[3]=X(" Docs "))]),_:3},8,["to"])):P("",!0)])],64)):P("",!0)])):P("",!0)}}),Se=["title"],ze=T({__name:"FilepathItem",props:{filepath:{},lineBreak:{type:Boolean},subpath:{type:Boolean}},setup(f){const n=f,e=_(()=>({path:n.filepath})),{copy:t}=Z();return(i,o)=>E((a(),p("button",{"font-mono":"","hover:underline":"",class:M(i.lineBreak?"":"ws-nowrap of-hidden truncate"),title:i.filepath,onClick:o[0]||(o[0]=l=>s(t)(i.filepath))},[X(x(s(e).path),1)],10,Se)),[[s(W),"Copy file path"]])}});function je(f){return typeof f=="string"?`'${f}'`:new Ie().serialize(f)}const Ie=function(){class f{#e=new Map;compare(e,t){const i=typeof e,o=typeof t;return i==="string"&&o==="string"?e.localeCompare(t):i==="number"&&o==="number"?e-t:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(t,!0))}serialize(e,t){if(e===null)return"null";switch(typeof e){case"string":return t?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const t=Object.prototype.toString.call(e);if(t!=="[object Object]")return this.serializeBuiltInType(t.length<10?`unknown:${t}`:t.slice(8,-1),e);const i=e.constructor,o=i===Object||i===void 0?"":i.name;if(o!==""&&globalThis[o]===i)return this.serializeBuiltInType(o,e);if(typeof e.toJSON=="function"){const l=e.toJSON();return o+(l!==null&&typeof l=="object"?this.$object(l):`(${this.serialize(l)})`)}return this.serializeObjectEntries(o,Object.entries(e))}serializeBuiltInType(e,t){const i=this["$"+e];if(i)return i.call(this,t);if(typeof t?.entries=="function")return this.serializeObjectEntries(e,t.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,t){const i=Array.from(t).sort((l,d)=>this.compare(l[0],d[0]));let o=`${e}{`;for(let l=0;l<i.length;l++){const[d,m]=i[l];o+=`${this.serialize(d,!0)}:${this.serialize(m)}`,l<i.length-1&&(o+=",")}return o+"}"}$object(e){let t=this.#e.get(e);return t===void 0&&(this.#e.set(e,`#${this.#e.size}`),t=this.serializeObject(e),this.#e.set(e,t)),t}$function(e){const t=Function.prototype.toString.call(e);return t.slice(-15)==="[native code] }"?`${e.name||""}()[native]`:`${e.name}(${e.length})${t.replace(/\s*\n\s*/g,"")}`}$Array(e){let t="[";for(let i=0;i<e.length;i++)t+=this.serialize(e[i]),i<e.length-1&&(t+=",");return t+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort((t,i)=>this.compare(t,i)))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}for(const n of["Error","RegExp","URL"])f.prototype["$"+n]=function(e){return`${n}(${e})`};for(const n of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])f.prototype["$"+n]=function(e){return`${n}[${e.join(",")}]`};for(const n of["BigInt64Array","BigUint64Array"])f.prototype["$"+n]=function(e){return`${n}[${e.join("n,")}${e.length>0?"n":""}]`};return f}(),Ve=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225],De=[1116352408,1899447441,-1245643825,-373957723,961987163,1508970993,-1841331548,-1424204075,-670586216,310598401,607225278,1426881987,1925078388,-2132889090,-1680079193,-1046744716,-459576895,-272742522,264347078,604807628,770255983,1249150122,1555081692,1996064986,-1740746414,-1473132947,-1341970488,-1084653625,-958395405,-710438585,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,-2117940946,-1838011259,-1564481375,-1474664885,-1035236496,-949202525,-778901479,-694614492,-200395387,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,-2067236844,-1933114872,-1866530822,-1538233109,-1090935817,-965641998],Te="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",U=[];class Oe{_data=new J;_hash=new J([...Ve]);_nDataBytes=0;_minBufferSize=0;finalize(n){n&&this._append(n);const e=this._nDataBytes*8,t=this._data.sigBytes*8;return this._data.words[t>>>5]|=128<<24-t%32,this._data.words[(t+64>>>9<<4)+14]=Math.floor(e/4294967296),this._data.words[(t+64>>>9<<4)+15]=e,this._data.sigBytes=this._data.words.length*4,this._process(),this._hash}_doProcessBlock(n,e){const t=this._hash.words;let i=t[0],o=t[1],l=t[2],d=t[3],m=t[4],v=t[5],j=t[6],I=t[7];for(let w=0;w<64;w++){if(w<16)U[w]=n[e+w]|0;else{const c=U[w-15],u=(c<<25|c>>>7)^(c<<14|c>>>18)^c>>>3,$=U[w-2],k=($<<15|$>>>17)^($<<13|$>>>19)^$>>>10;U[w]=u+U[w-7]+k+U[w-16]}const O=m&v^~m&j,K=i&o^i&l^o&l,g=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),h=(m<<26|m>>>6)^(m<<21|m>>>11)^(m<<7|m>>>25),y=I+h+O+De[w]+U[w],V=g+K;I=j,j=v,v=m,m=d+y|0,d=l,l=o,o=i,i=y+V|0}t[0]=t[0]+i|0,t[1]=t[1]+o|0,t[2]=t[2]+l|0,t[3]=t[3]+d|0,t[4]=t[4]+m|0,t[5]=t[5]+v|0,t[6]=t[6]+j|0,t[7]=t[7]+I|0}_append(n){typeof n=="string"&&(n=J.fromUtf8(n)),this._data.concat(n),this._nDataBytes+=n.sigBytes}_process(n){let e,t=this._data.sigBytes/64;n?t=Math.ceil(t):t=Math.max((t|0)-this._minBufferSize,0);const i=t*16,o=Math.min(i*4,this._data.sigBytes);if(i){for(let l=0;l<i;l+=16)this._doProcessBlock(this._data.words,l);e=this._data.words.splice(0,i),this._data.sigBytes-=o}return new J(e,o)}}class J{words;sigBytes;constructor(n,e){n=this.words=n||[],this.sigBytes=e===void 0?n.length*4:e}static fromUtf8(n){const e=unescape(encodeURIComponent(n)),t=e.length,i=[];for(let o=0;o<t;o++)i[o>>>2]|=(e.charCodeAt(o)&255)<<24-o%4*8;return new J(i,t)}toBase64(){const n=[];for(let e=0;e<this.sigBytes;e+=3){const t=this.words[e>>>2]>>>24-e%4*8&255,i=this.words[e+1>>>2]>>>24-(e+1)%4*8&255,o=this.words[e+2>>>2]>>>24-(e+2)%4*8&255,l=t<<16|i<<8|o;for(let d=0;d<4&&e*8+d*6<this.sigBytes*8;d++)n.push(Te.charAt(l>>>6*(3-d)&63))}return n.join("")}concat(n){if(this.words[this.sigBytes>>>2]&=4294967295<<32-this.sigBytes%4*8,this.words.length=Math.ceil(this.sigBytes/4),this.sigBytes%4)for(let e=0;e<n.sigBytes;e++){const t=n.words[e>>>2]>>>24-e%4*8&255;this.words[this.sigBytes+e>>>2]|=t<<24-(this.sigBytes+e)%4*8}else for(let e=0;e<n.sigBytes;e+=4)this.words[this.sigBytes+e>>>2]=n.words[e>>>2];this.sigBytes+=n.sigBytes}}function Ue(f){return new Oe().finalize(f).toBase64()}function Fe(f){return Ue(je(f))}const Ee=T({__name:"AssetFontPreview",props:{asset:{}},setup(f){const n=f,e=_(()=>`devtools-assets-${Fe(n.asset)}`);return ue(_(()=>`
  @font-face {
    font-family: '${e.value}';
    src: url('${n.asset.publicPath}');
  }
`)),(t,i)=>(a(),p("div",{"of-hidden":"",style:se({fontFamily:`'${s(e)}'`})}," Aa Bb Cc Dd Ee Ff Gg Hh Ii Jj Kk Ll Mm Nn Oo Pp Qq Rr Ss Tt Uu Vv Ww Xx Yy Zz ",4))}}),Me={flex:"","items-center":"","justify-center":"","of-hidden":"","bg-active":"","object-cover":"",p1:""},Le=["src"],Re={key:2,"i-carbon-document":"","text-3xl":"",op20:""},Ne={key:3,"w-full":"","self-start":"",p4:""},qe=["textContent"],Ge={key:4},Je=["src","autoplay","controls"],Xe={key:5},He={key:0,"i-carbon-volume-up":"","text-3xl":"",op20:""},Ke=["src"],We={key:6,"i-vscode-icons-file-type-wasm":"","text-3xl":""},Ye={key:7,"i-carbon-help":"","text-3xl":"",op20:""},ie=T({__name:"AssetPreview",props:{asset:{},textContent:{},detail:{type:Boolean}},setup(f){return(n,e)=>{const t=Ee;return a(),p("div",Me,[n.asset.type==="image"?(a(),p("img",{key:0,src:n.asset.publicPath},null,8,Le)):n.asset.type==="font"?(a(),S(t,{key:n.asset.publicPath,asset:n.asset,"self-stretch":"",p2:"","text-2xl":""},null,8,["asset"])):n.asset.type==="text"&&!n.textContent?(a(),p("div",Re)):n.asset.type==="text"&&n.textContent?(a(),p("div",Ne,[r("pre",{"max-h-10rem":"","of-hidden":"","text-xs":"","font-mono":"",textContent:x(n.textContent)},null,8,qe)])):n.asset.type==="video"?(a(),p("div",Ge,[r("video",{src:n.asset.publicPath,autoplay:n.detail,controls:n.detail},null,8,Je)])):n.asset.type==="audio"?(a(),p("div",Xe,[n.detail?(a(),p("audio",{key:1,src:n.asset.publicPath,controls:""},null,8,Ke)):(a(),p("div",He))])):n.asset.type==="wasm"?(a(),p("div",We)):(a(),p("div",Ye))])}}}),Qe={flex:"~ col gap-4","min-h-full":"","w-full":"","of-hidden":"",p4:""},Ze={flex:"~","items-center":"","justify-center":""},et={"max-w-full":"","w-full":"","table-fixed":""},tt={flex:"~ gap-1","w-full":"","items-center":""},st={flex:"~ gap-1","w-full":"","items-center":"","of-hidden":""},nt={"flex-auto":"","of-hidden":"",truncate:"","ws-pre":"","font-mono":""},ot={capitalize:""},it={key:0},rt={op70:""},lt={flex:"~ gap2 wrap"},at=T({__name:"AssetDetails",props:{modelValue:{}},setup(f,{emit:n}){const e=f,t=n,i=de(),o=ne(e,"modelValue",t,{passive:!0}),l=_(()=>i.vitePluginDetected.value),d=Y(()=>H.value.getAssetImporters(o.value.publicPath).then(c=>c),[]),m=_(()=>ce.value),v=Y(()=>{if(o.value.type==="image")return H.value.getImageMeta(o.value.filePath).then(c=>c)}),j=D(),I=D(0),w=Y(async()=>{if(o.value.type!=="text")return;I.value;const c=await H.value.getTextAssetContent(o.value.filePath).then(u=>u);return j.value=c,c}),O=_(()=>{const c=[];if(o.value.type==="image"){const u=v.value?.width?`
  width="${v.value.width}"
  height="${v.value.height}" `:" ";return c.push({lang:"vue-html",code:`<img${u}
  src="${o.value.publicPath}"
/>`,name:"Plain Image"}),c}return c.push({lang:"html",code:`<a download href="${o.value.publicPath}">
  Download ${o.value.path.split("/").slice(-1)[0]}
</a>`,name:"Download link"}),c}),{copy:K}=Z(),g=pe(()=>o.value.mtime),h=_(()=>{const c=o.value.size;return c<1024?`${c} B`:c<1024*1024?`${(c/1024).toFixed(2)} KB`:`${(c/1024/1024).toFixed(2)} MB`}),y=_(()=>{if(!v.value?.width||!v.value?.height)return"";const c=($,k)=>k?c(k,$%k):$,u=c(v.value.width,v.value.height);return u>3?`${v.value.width/u}:${v.value.height/u}`:""}),V=_(()=>["image","text","video","audio","font"].includes(o.value.type));return(c,u)=>{const $=ie,k=ze,R=oe("RouterLink"),b=Pe;return a(),p("div",Qe,[s(V)?(a(),p(z,{key:0},[u[2]||(u[2]=r("div",{flex:"~ gap2","mb--2":"","items-center":"",op50:""},[r("div",{"x-divider":""}),r("div",{"flex-none":""}," Preview "),r("div",{"x-divider":""})],-1)),r("div",Ze,[C($,{detail:"","max-h-80":"","min-h-20":"","min-w-20":"","w-auto":"",rounded:"",border:"~ base",asset:s(o),"text-content":s(w)},null,8,["asset","text-content"])])],64)):P("",!0),u[12]||(u[12]=r("div",{flex:"~ gap2","mb--2":"","items-center":"",op50:""},[r("div",{"x-divider":""}),r("div",{"flex-none":""}," Details "),r("div",{"x-divider":""})],-1)),r("table",et,[r("tbody",null,[r("tr",null,[u[3]||(u[3]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Filepath ",-1)),r("td",null,[r("div",tt,[C(k,{filepath:s(o).filePath,"text-left":""},null,8,["filepath"]),s(l)&&s(m)?E((a(),S(s(q),{key:0,title:"Open in Editor",icon:"i-carbon-launch",action:"","flex-none":"",border:!1,onClick:u[0]||(u[0]=B=>s(ee)(s(o).filePath))},null,512)),[[s(W),"Open in Editor"]]):P("",!0)])])]),r("tr",null,[u[4]||(u[4]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Public Path ",-1)),r("td",null,[r("div",st,[r("div",nt,x(s(o).publicPath),1),E(C(s(q),{title:"Copy public path",icon:"i-carbon-copy",action:"",mr1:"","mt--2px":"","flex-none":"",border:!1,onClick:u[1]||(u[1]=B=>s(K)(s(o).publicPath,{type:"assets-public-path"}))},null,512),[[s(W),"Copy public path"]]),C(R,{to:s(o).publicPath,target:"_blank"},{default:A(()=>[E(C(s(q),{icon:"i-carbon-launch",action:"","flex-none":"",border:!1,title:"Open in Browser"},null,512),[[s(W),"Open in Browser"]])]),_:1},8,["to"])])])]),r("tr",null,[u[5]||(u[5]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Type ",-1)),r("td",ot,x(s(o).type),1)]),s(v)?.width?(a(),p(z,{key:0},[r("tr",null,[u[6]||(u[6]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Image Size ",-1)),r("td",null,x(s(v).width)+" x "+x(s(v).height),1)]),s(y)?(a(),p("tr",it,[u[7]||(u[7]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Aspect Ratio ",-1)),r("td",null,x(s(y)),1)])):P("",!0)],64)):P("",!0),r("tr",null,[u[8]||(u[8]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," File size ",-1)),r("td",null,x(s(h)),1)]),r("tr",null,[u[9]||(u[9]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"",op50:""}," Last modified ",-1)),r("td",null,[X(x(new Date(s(o).mtime).toLocaleString())+" ",1),r("span",rt,"("+x(s(g))+")",1)])]),r("tr",null,[u[10]||(u[10]=r("td",{"w-30":"","ws-nowrap":"",pr5:"","text-right":"","align-top":"",op50:""}," Importers ",-1)),r("td",null,[s(d).length>0?(a(!0),p(z,{key:0},F(s(d),B=>(a(),p("div",{key:B.url,flex:"~ gap-1","w-full":"","items-center":""},[C(k,{filepath:B.id||B.url,"text-left":""},null,8,["filepath"]),s(i).vitePluginDetected.value&&s(m)&&B.id?E((a(),S(s(q),{key:0,title:"Open in Editor",icon:"i-carbon-launch",action:"","flex-none":"",border:!1,onClick:N=>s(ee)(B.id)},null,8,["onClick"])),[[s(W),"Open in Editor"]]):P("",!0)]))),128)):(a(),p(z,{key:1},[X(" None ")],64))])])])]),u[13]||(u[13]=r("div",{flex:"~ gap2","mb--2":"","items-center":"",op50:""},[r("div",{"x-divider":""}),r("div",{"flex-none":""}," Actions "),r("div",{"x-divider":""})],-1)),r("div",lt,[C(s(Q),{to:s(o).publicPath,download:"",target:"_blank"},{icon:A(()=>[L(c.$slots,"i-carbon-download")]),default:A(()=>[u[11]||(u[11]=X(" Download "))]),_:3},8,["to"])]),u[14]||(u[14]=r("div",{"flex-auto":""},null,-1)),s(O).length?(a(),S(b,{key:1,border:"t base","mx--4":"","mb--4":"","code-snippets":s(O)},null,8,["code-snippets"])):P("",!0)])}}}),ut=T({__name:"AssetListItem",props:{item:{},index:{default:0},modelValue:{}},setup(f,{emit:n}){const e=f,i=ne(e,"modelValue",n,{passive:!0}),o=_(()=>e.item?.children?.length),l=D(!0),d=_(()=>o.value?"i-carbon-folder":e.item.type==="image"?"i-carbon-image":e.item.type==="video"?"i-carbon-video":e.item.type==="audio"?"i-carbon-volume-up":e.item.type==="font"?"i-carbon-text-small-caps":e.item.type==="text"?"i-carbon-document":e.item.type==="json"?"i-carbon-json":e.item.type==="wasm"?"i-vscode-icons-file-type-wasm":"i-carbon-document-blank");return(m,v)=>{const j=oe("AssetListItem",!0);return a(),p("div",null,[r("button",{flex:"~ gap-2","w-full":"","items-center":"",hover:"bg-active",px4:"",py1:"",style:se({paddingLeft:`calc(1rem + ${m.index*1.5}em)`}),class:M({"bg-active":!s(o)&&s(i)?.filePath===m.item?.filePath}),border:"b base",onClick:v[0]||(v[0]=I=>s(o)?l.value=!s(l):i.value=m.item)},[r("div",{class:M(s(d))},null,2),r("span",{class:M({"flex items-center":s(o)}),"flex-auto":"","text-start":"","text-sm":"","font-mono":""},x(m.item.path),3),s(o)?(a(),S(s(q),{key:0,icon:"carbon:chevron-right","transform-rotate":s(l)?90:0,transition:""},null,8,["transform-rotate"])):P("",!0)],6),s(l)?L(m.$slots,"default",{key:0},()=>[(a(!0),p(z,null,F(m.item?.children,I=>(a(),S(j,{key:I.filepath,modelValue:s(i),"onUpdate:modelValue":v[1]||(v[1]=w=>G(i)?i.value=w:null),item:I,index:m.index+1},null,8,["modelValue","item","index"]))),128))]):P("",!0)])}}}),dt={flex:"~ col gap-1",hover:"bg-active","items-center":"","of-hidden":"",rounded:"",p2:""},ct={"w-full":"","of-hidden":"",truncate:"","ws-nowrap":"","text-center":"","text-xs":""},pt=T({__name:"AssetGridItem",props:{asset:{},folder:{}},setup(f){const n=f,e=_(()=>n.folder&&n.asset.path.startsWith(n.folder)?n.asset.path.slice(n.folder.length):n.asset.path);return(t,i)=>{const o=ie;return a(),p("button",dt,[C(o,{"h-30":"","w-30":"",rounded:"",border:"~ base",asset:t.asset},null,8,["asset"]),r("div",ct,x(s(e)),1)])}}}),ht={flex:"~ col gap2",border:"b base","flex-1":"",p4:"","navbar-glass":""},ft={flex:"~ gap4","items-center":""},mt=T({__name:"Navbar",props:{search:{},noPadding:{type:Boolean}},emits:["update:search"],setup(f,{emit:n}){const e=f,t=n,i=D(e.search);return te(()=>e.search,o=>{i.value=o}),te(i,()=>{t("update:search",i.value)}),(o,l)=>(a(),p("div",ht,[r("div",ft,[L(o.$slots,"search",{},()=>[o.search!==void 0?(a(),S(s(he),{key:0,modelValue:s(i),"onUpdate:modelValue":l[0]||(l[0]=d=>G(i)?i.value=d:null),placeholder:"Search...","left-icon":"i-carbon-search",class:M(["flex-auto",{"px-5 py-2":!o.noPadding}])},null,8,["modelValue","class"])):P("",!0)]),L(o.$slots,"actions")]),L(o.$slots,"default")]))}}),vt={block:"","h-full":"","of-hidden":"",class:"drawer-container relative"},yt={"h-full":"","w-full":"","of-auto":""},gt={"flex-none":"",flex:"~ gap2 items-center","text-lg":""},bt={flex:"~ items-center justify-center",absolute:"","bottom-0":"","right-2px":"","h-4":"","w-4":"","rounded-full":"","bg-primary-800":"","text-8px":"","text-white":""},wt={"w-full":"",flex:"~ gap-2 items-center",rounded:"",px2:"",py2:""},_t={"text-xs":"",op75:""},$t={op50:""},xt={key:0},kt={"mt--4":"",px2:"",grid:"~ cols-minmax-8rem"},Bt={key:1,p2:"",grid:"~ cols-minmax-8rem"},Ct={key:1},At=50,It=T({__name:"assets",setup(f){const n=D(""),e=D(),t=D("grid"),i=D([]),o=_(()=>{const g=[];for(const h of i.value||[]){const y=h.path.split(".").pop();y&&!g.find(V=>V.value===y)&&g.push({label:y,value:y})}return g}),l=D([]);fe(()=>o.value,g=>{l.value=g.map(h=>h.value)});const d=D(),m=_(()=>new me(i.value||[],{keys:["path"]})),v=_(()=>(n.value?m.value.search(n.value).map(h=>h.item):i.value||[]).filter(h=>{const y=h.path.split(".").pop();return!y||l.value.includes(y)})),j=_(()=>{const g={};for(const h of v.value){const y=`${h.relativePath.split("/").slice(0,-1).join("/")}/`;g[y]||(g[y]=[]),g[y].push(h)}return Object.entries(g).sort(([h],[y])=>h.localeCompare(y))}),I=_(()=>{const g={children:[]},h=(y,V,c)=>{const[u,...$]=V;let k=y.children.find(R=>R.path===u);k||(k={...c,path:u,children:[]},y.children.push(k)),$.length>1?h(k,$,c):$.length===1&&k.children.push({...c,path:$[0]})};return v.value.forEach(y=>{const V=y.relativePath.split("/").filter(c=>c!=="");h(g,V,y)}),g.children});function w(){H.value.getStaticAssets().then(g=>{i.value=g})}function O(){w()}ve(()=>{w(),H.functions.on("assetsUpdated",O)});function K(){t.value=t.value==="list"?"grid":"list"}return ye(()=>{H.functions.off("assetsUpdated",O)}),(g,h)=>{const y=xe,V=mt,c=pt,u=$e,$=ut,k=at,R=_e("tooltip");return a(),p("div",vt,[r("div",yt,[C(V,{ref_key:"navbar",ref:e,search:s(n),"onUpdate:search":h[1]||(h[1]=b=>G(n)?n.value=b:null),pb2:"","no-padding":!0},{actions:A(()=>[r("div",gt,[C(s(ge),{modelValue:s(l),"onUpdate:modelValue":h[0]||(h[0]=b=>G(l)?l.value=b:null),multiple:!0,options:s(o)},{button:A(()=>[E((a(),S(y,{icon:"i-carbon-filter hover:op50",border:!1,title:"Filter",relative:"","cursor-pointer":"",p2:"","text-lg":"",onClick:()=>{}},{default:A(()=>[r("span",bt,x(s(l).length),1)]),_:1})),[[R,"Filter",void 0,{"bottom-end":!0}]])]),item:A(({item:b,active:B})=>[r("div",wt,[C(s(be),{"model-value":B},null,8,["model-value"]),r("span",_t,x(b.label),1)])]),_:1},8,["modelValue","options"]),E(C(s(q),{border:!1,icon:s(t)==="grid"?"i-carbon-list":"i-carbon-grid",title:"Toggle view",action:"","cursor-pointer":"","text-lg":"",onClick:K},null,8,["icon"]),[[R,"Toggle View",void 0,{"bottom-end":!0}]])])]),default:A(()=>[r("div",$t,[s(n)?(a(),p("span",xt,x(s(v).length)+" matched · ",1)):P("",!0),r("span",null,x(s(i)?.length)+" assets in total",1)])]),_:1},8,["search"]),s(t)==="grid"?(a(),p(z,{key:0},[s(j).length>1?(a(!0),p(z,{key:0},F(s(j),([b,B])=>(a(),S(u,{key:b,text:b,description:`${B.length} items`,open:B.length<=At,padding:!1},{default:A(()=>[r("div",kt,[(a(!0),p(z,null,F(B,N=>(a(),S(c,{key:N.path,asset:N,folder:b,onClick:Pt=>d.value=N},null,8,["asset","folder","onClick"]))),128))])]),_:2},1032,["text","description","open"]))),128)):(a(),p("div",Bt,[(a(!0),p(z,null,F(s(v),b=>(a(),S(c,{key:b.path,asset:b,onClick:B=>d.value=b},null,8,["asset","onClick"]))),128))]))],64)):(a(),p("div",Ct,[(a(!0),p(z,null,F(s(I),(b,B)=>(a(),S($,{key:B,modelValue:s(d),"onUpdate:modelValue":h[2]||(h[2]=N=>G(d)?d.value=N:null),item:b},null,8,["modelValue","item"]))),128))]))]),C(s(we),{"model-value":!!s(d),top:s(e),permanent:"","mount-to":".drawer-container",position:"absolute","content-class":"w120 text-sm","onUpdate:modelValue":h[4]||(h[4]=b=>{b||(d.value=void 0)})},{default:A(()=>[s(d)?(a(),S(k,{key:0,modelValue:s(d),"onUpdate:modelValue":h[3]||(h[3]=b=>G(d)?d.value=b:null)},null,8,["modelValue"])):P("",!0)]),_:1},8,["model-value","top"])])}}});export{It as default};
